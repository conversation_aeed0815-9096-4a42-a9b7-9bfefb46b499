#!/bin/bash

# Online Grocery System - Main Startup Script
# This script starts both backend and frontend applications

echo "=================================================="
echo "Online Grocery Ordering System"
echo "Complete Full-Stack Application"
echo "=================================================="

# Function to check if a command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Function to check if a port is in use
port_in_use() {
    lsof -i :$1 >/dev/null 2>&1
}

# Check system requirements
echo "Checking system requirements..."

# Check Java
if ! command_exists java; then
    echo "❌ Java is not installed. Please install Java 17 or higher."
    exit 1
fi

JAVA_VERSION=$(java -version 2>&1 | awk -F '"' '/version/ {print $2}' | cut -d'.' -f1)
if [ "$JAVA_VERSION" -lt 17 ]; then
    echo "❌ Java 17 or higher is required. Current version: $JAVA_VERSION"
    exit 1
fi
echo "✅ Java $JAVA_VERSION detected"

# Check Maven
if ! command_exists mvn; then
    echo "❌ Maven is not installed. Please install Apache Maven."
    exit 1
fi
echo "✅ Maven detected"

# Check Node.js
if ! command_exists node; then
    echo "❌ Node.js is not installed. Please install Node.js 18 or higher."
    exit 1
fi

NODE_VERSION=$(node -v | cut -d'v' -f2 | cut -d'.' -f1)
if [ "$NODE_VERSION" -lt 18 ]; then
    echo "❌ Node.js 18 or higher is required. Current version: $(node -v)"
    exit 1
fi
echo "✅ Node.js $(node -v) detected"

# Check npm
if ! command_exists npm; then
    echo "❌ npm is not installed."
    exit 1
fi
echo "✅ npm $(npm -v) detected"

# Check if ports are available
if port_in_use 8080; then
    echo "❌ Port 8080 is already in use. Please stop the service using this port."
    exit 1
fi

if port_in_use 4200; then
    echo "❌ Port 4200 is already in use. Please stop the service using this port."
    exit 1
fi

echo "✅ Ports 8080 and 4200 are available"
echo ""

# Make scripts executable
chmod +x scripts/start-backend.sh
chmod +x scripts/start-frontend.sh

# Install frontend dependencies
echo "Installing frontend dependencies..."
cd frontend
if [ ! -d "node_modules" ]; then
    npm install
    if [ $? -ne 0 ]; then
        echo "❌ Failed to install frontend dependencies"
        exit 1
    fi
fi

# Install Angular CLI if not present
if ! command_exists ng; then
    echo "Installing Angular CLI..."
    npm install -g @angular/cli@17
    if [ $? -ne 0 ]; then
        echo "❌ Failed to install Angular CLI"
        exit 1
    fi
fi

cd ..

# Build backend
echo "Building backend application..."
cd backend
mvn clean compile
if [ $? -ne 0 ]; then
    echo "❌ Failed to build backend application"
    exit 1
fi
cd ..

echo "✅ All dependencies installed and applications built successfully"
echo ""

# Start applications
echo "=================================================="
echo "Starting Applications"
echo "=================================================="

# Function to cleanup on exit
cleanup() {
    echo ""
    echo "Shutting down applications..."
    kill $BACKEND_PID 2>/dev/null
    kill $FRONTEND_PID 2>/dev/null
    echo "Applications stopped."
    exit 0
}

# Set trap to cleanup on script exit
trap cleanup SIGINT SIGTERM

# Start backend in background
echo "Starting backend server..."
cd backend
mvn spring-boot:run > ../logs/backend.log 2>&1 &
BACKEND_PID=$!
cd ..

# Wait for backend to start
echo "Waiting for backend to start..."
sleep 10

# Check if backend is running
if ! kill -0 $BACKEND_PID 2>/dev/null; then
    echo "❌ Backend failed to start. Check logs/backend.log for details."
    exit 1
fi

# Start frontend in background
echo "Starting frontend server..."
cd frontend
ng serve --host 0.0.0.0 --port 4200 > ../logs/frontend.log 2>&1 &
FRONTEND_PID=$!
cd ..

# Wait for frontend to start
echo "Waiting for frontend to start..."
sleep 15

# Check if frontend is running
if ! kill -0 $FRONTEND_PID 2>/dev/null; then
    echo "❌ Frontend failed to start. Check logs/frontend.log for details."
    kill $BACKEND_PID 2>/dev/null
    exit 1
fi

echo ""
echo "=================================================="
echo "🎉 Online Grocery System Started Successfully!"
echo "=================================================="
echo ""
echo "📱 Frontend Application: http://localhost:4200"
echo "🔧 Backend API: http://localhost:8080/api"
echo "🗄️  H2 Database Console: http://localhost:8080/api/h2-console"
echo ""
echo "👤 Demo Login Credentials:"
echo "   Admin: admin / admin123"
echo "   Customer: <EMAIL> / secret"
echo ""
echo "📋 Available Features:"
echo "   ✅ Customer Registration & Login"
echo "   ✅ Product Search & Management"
echo "   ✅ Order Management"
echo "   ✅ Admin Panel"
echo "   ✅ SQL Injection Prevention"
echo ""
echo "📝 Logs:"
echo "   Backend: logs/backend.log"
echo "   Frontend: logs/frontend.log"
echo ""
echo "Press Ctrl+C to stop all services"
echo "=================================================="

# Wait for user to stop the services
wait
