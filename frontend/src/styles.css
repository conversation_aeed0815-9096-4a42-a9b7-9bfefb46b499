/* Global Styles */
html, body { 
  height: 100%; 
  margin: 0; 
  font-family: <PERSON><PERSON>, "Helvetica Neue", sans-serif; 
}

/* Custom Snackbar Styles */
.success-snackbar {
  background-color: #4caf50 !important;
  color: white !important;
}

.error-snackbar {
  background-color: #f44336 !important;
  color: white !important;
}

.warning-snackbar {
  background-color: #ff9800 !important;
  color: white !important;
}

.info-snackbar {
  background-color: #2196f3 !important;
  color: white !important;
}

/* Material Design Overrides */
.mat-mdc-card {
  box-shadow: 0 2px 8px rgba(0,0,0,0.1) !important;
  border-radius: 8px !important;
}

.mat-mdc-raised-button {
  box-shadow: 0 2px 4px rgba(0,0,0,0.2) !important;
}

.mat-mdc-raised-button:hover {
  box-shadow: 0 4px 8px rgba(0,0,0,0.3) !important;
}

/* Custom Button Styles */
.mat-mdc-button, .mat-mdc-raised-button {
  border-radius: 6px !important;
  font-weight: 500 !important;
  text-transform: none !important;
}

/* Form Field Styles */
.mat-mdc-form-field {
  width: 100%;
}

.mat-mdc-form-field .mat-mdc-text-field-wrapper {
  border-radius: 6px !important;
}

/* Table Styles */
.mat-mdc-table {
  border-radius: 8px !important;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1) !important;
}

.mat-mdc-header-row {
  background-color: #f5f5f5 !important;
}

.mat-mdc-row:hover {
  background-color: #f9f9f9 !important;
}

/* Toolbar Styles */
.mat-toolbar {
  box-shadow: 0 2px 4px rgba(0,0,0,0.1) !important;
}

/* Card Header Styles */
.mat-mdc-card-header .mat-mdc-card-title {
  font-size: 1.5rem !important;
  font-weight: 500 !important;
  color: #333 !important;
}

.mat-mdc-card-header .mat-mdc-card-subtitle {
  color: #666 !important;
  margin-top: 4px !important;
}

/* Loading Animation */
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-spinner {
  animation: spin 1s linear infinite;
}

/* Responsive Utilities */
@media (max-width: 768px) {
  .mat-mdc-card {
    margin: 8px !important;
  }
  
  .mat-toolbar {
    padding: 0 8px !important;
  }
  
  .mat-mdc-card-header {
    padding: 16px 16px 0 16px !important;
  }
  
  .mat-mdc-card-content {
    padding: 16px !important;
  }
  
  .mat-mdc-card-actions {
    padding: 8px 16px 16px 16px !important;
  }
}

/* Custom Scrollbar */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Focus Styles */
.mat-mdc-button:focus,
.mat-mdc-raised-button:focus,
.mat-mdc-form-field:focus-within {
  outline: 2px solid #1976d2;
  outline-offset: 2px;
}

/* Accessibility */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* Print Styles */
@media print {
  .mat-toolbar,
  .mat-mdc-card-actions,
  button {
    display: none !important;
  }
  
  .mat-mdc-card {
    box-shadow: none !important;
    border: 1px solid #ddd !important;
  }
}
