import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { Product, ProductRegistrationRequest, ProductUpdateRequest } from '../models/product.model';
import { AuthService } from './auth.service';

@Injectable({
  providedIn: 'root'
})
export class ProductService {
  private apiUrl = 'http://localhost:8080/api/products';

  constructor(
    private http: HttpClient,
    private authService: AuthService
  ) {}

  /**
   * US006: Search Product by Name
   */
  searchProductsByName(name: string): Observable<any> {
    const headers = this.authService.getAuthHeaders();
    return this.http.get(`${this.apiUrl}/search?name=${encodeURIComponent(name)}`, { headers });
  }

  /**
   * US007: Register Product (Admin only)
   */
  registerProduct(product: ProductRegistrationRequest): Observable<any> {
    const headers = this.authService.getAuthHeaders();
    return this.http.post(`${this.apiUrl}/register`, product, { headers });
  }

  /**
   * US008: Update Product (Admin only)
   */
  updateProduct(productId: number, product: ProductUpdateRequest): Observable<any> {
    const headers = this.authService.getAuthHeaders();
    return this.http.put(`${this.apiUrl}/update/${productId}`, product, { headers });
  }

  /**
   * US009: Delete Product (Admin only)
   */
  deleteProduct(productId: number): Observable<any> {
    const headers = this.authService.getAuthHeaders();
    return this.http.delete(`${this.apiUrl}/delete/${productId}`, { headers });
  }

  /**
   * Get product by ID
   */
  getProductById(productId: number): Observable<any> {
    const headers = this.authService.getAuthHeaders();
    return this.http.get(`${this.apiUrl}/${productId}`, { headers });
  }

  /**
   * Get all products
   */
  getAllProducts(): Observable<any> {
    const headers = this.authService.getAuthHeaders();
    return this.http.get(`${this.apiUrl}`, { headers });
  }

  /**
   * Get available products only
   */
  getAvailableProducts(): Observable<any> {
    const headers = this.authService.getAuthHeaders();
    return this.http.get(`${this.apiUrl}/available`, { headers });
  }

  /**
   * Update product quantity (Admin only)
   */
  updateProductQuantity(productId: number, quantity: number): Observable<any> {
    const headers = this.authService.getAuthHeaders();
    return this.http.put(`${this.apiUrl}/${productId}/quantity?quantity=${quantity}`, {}, { headers });
  }

  /**
   * Validate product input
   */
  validateProductData(product: ProductRegistrationRequest | ProductUpdateRequest): string[] {
    const errors: string[] = [];

    // Validate product name
    if (!product.productName || product.productName.trim().length < 2) {
      errors.push('Product name must be at least 2 characters long');
    }
    if (product.productName && product.productName.length > 100) {
      errors.push('Product name cannot exceed 100 characters');
    }

    // Validate price
    if (!product.price || product.price <= 0) {
      errors.push('Price must be greater than 0');
    }

    // Validate quantity
    if (product.quantity === null || product.quantity === undefined || product.quantity < 0) {
      errors.push('Quantity cannot be negative');
    }

    // Validate reserved quantity
    if (product.reserved !== null && product.reserved !== undefined && product.reserved < 0) {
      errors.push('Reserved quantity cannot be negative');
    }

    // Validate that reserved quantity doesn't exceed total quantity
    if (product.reserved !== null && product.reserved !== undefined && 
        product.quantity !== null && product.quantity !== undefined && 
        product.reserved > product.quantity) {
      errors.push('Reserved quantity cannot exceed total quantity');
    }

    return errors;
  }

  /**
   * Format price for display
   */
  formatPrice(price: number): string {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(price);
  }

  /**
   * Check if product is available
   */
  isProductAvailable(product: Product): boolean {
    if (!product.quantity || product.quantity <= 0) {
      return false;
    }
    
    const reserved = product.reserved || 0;
    return (product.quantity - reserved) > 0;
  }

  /**
   * Get available quantity
   */
  getAvailableQuantity(product: Product): number {
    if (!product.quantity) {
      return 0;
    }
    
    const reserved = product.reserved || 0;
    return Math.max(0, product.quantity - reserved);
  }
}
