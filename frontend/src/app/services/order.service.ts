import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { Order, CreateOrderRequest, OrderUpdateRequest } from '../models/order.model';
import { AuthService } from './auth.service';

@Injectable({
  providedIn: 'root'
})
export class OrderService {
  private apiUrl = 'http://localhost:8080/api/orders';

  constructor(
    private http: HttpClient,
    private authService: AuthService
  ) {}

  /**
   * Create a new order
   */
  createOrder(orderRequest: CreateOrderRequest): Observable<any> {
    const headers = this.authService.getAuthHeaders();
    return this.http.post(`${this.apiUrl}/create`, orderRequest, { headers });
  }

  /**
   * Get order by ID
   */
  getOrderById(orderId: number): Observable<any> {
    const headers = this.authService.getAuthHeaders();
    return this.http.get(`${this.apiUrl}/${orderId}`, { headers });
  }

  /**
   * Get all orders (Admin only)
   */
  getAllOrders(): Observable<any> {
    const headers = this.authService.getAuthHeaders();
    return this.http.get(`${this.apiUrl}`, { headers });
  }

  /**
   * Update order status (Admin only)
   */
  updateOrderStatus(orderId: number, status: string): Observable<any> {
    const headers = this.authService.getAuthHeaders();
    return this.http.put(`${this.apiUrl}/${orderId}/status?status=${encodeURIComponent(status)}`, {}, { headers });
  }

  /**
   * Cancel order
   */
  cancelOrder(orderId: number): Observable<any> {
    const headers = this.authService.getAuthHeaders();
    return this.http.put(`${this.apiUrl}/${orderId}/cancel`, {}, { headers });
  }

  /**
   * Update order (Admin only)
   */
  updateOrder(orderId: number, order: OrderUpdateRequest): Observable<any> {
    const headers = this.authService.getAuthHeaders();
    return this.http.put(`${this.apiUrl}/${orderId}`, order, { headers });
  }

  /**
   * Delete order (Admin only)
   */
  deleteOrder(orderId: number): Observable<any> {
    const headers = this.authService.getAuthHeaders();
    return this.http.delete(`${this.apiUrl}/${orderId}`, { headers });
  }

  /**
   * Validate order input
   */
  validateOrderData(order: CreateOrderRequest): string[] {
    const errors: string[] = [];

    // Validate customer ID
    if (!order.customerId || order.customerId <= 0) {
      errors.push('Customer ID is required');
    }

    // Validate product ID
    if (!order.productId || order.productId <= 0) {
      errors.push('Product ID is required');
    }

    // Validate quantity
    if (!order.quantity || order.quantity <= 0) {
      errors.push('Quantity must be greater than 0');
    }

    return errors;
  }

  /**
   * Format order date for display
   */
  formatOrderDate(dateString: string): string {
    const date = new Date(dateString);
    return new Intl.DateTimeFormat('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    }).format(date);
  }

  /**
   * Format order amount for display
   */
  formatOrderAmount(amount: number): string {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  }

  /**
   * Get order status color for UI
   */
  getOrderStatusColor(status: string): string {
    switch (status?.toUpperCase()) {
      case 'PENDING':
        return 'orange';
      case 'CONFIRMED':
        return 'blue';
      case 'SHIPPED':
        return 'purple';
      case 'DELIVERED':
        return 'green';
      case 'CANCELLED':
        return 'red';
      default:
        return 'gray';
    }
  }

  /**
   * Get order status display text
   */
  getOrderStatusText(status: string): string {
    switch (status?.toUpperCase()) {
      case 'PENDING':
        return 'Pending';
      case 'CONFIRMED':
        return 'Confirmed';
      case 'SHIPPED':
        return 'Shipped';
      case 'DELIVERED':
        return 'Delivered';
      case 'CANCELLED':
        return 'Cancelled';
      default:
        return status || 'Unknown';
    }
  }

  /**
   * Check if order can be cancelled
   */
  canCancelOrder(status: string): boolean {
    const nonCancellableStatuses = ['SHIPPED', 'DELIVERED', 'CANCELLED'];
    return !nonCancellableStatuses.includes(status?.toUpperCase());
  }

  /**
   * Get available order statuses for admin
   */
  getAvailableStatuses(): string[] {
    return ['PENDING', 'CONFIRMED', 'SHIPPED', 'DELIVERED', 'CANCELLED'];
  }
}
