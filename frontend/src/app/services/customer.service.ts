import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { Customer, CustomerRegistrationRequest, CustomerUpdateRequest } from '../models/customer.model';
import { Order } from '../models/order.model';
import { AuthService } from './auth.service';

@Injectable({
  providedIn: 'root'
})
export class CustomerService {
  private apiUrl = 'http://localhost:8080/api/customers';

  constructor(
    private http: HttpClient,
    private authService: AuthService
  ) {}

  /**
   * US002: Customer Registration
   */
  registerCustomer(customer: CustomerRegistrationRequest): Observable<any> {
    return this.http.post(`${this.apiUrl}/register`, customer);
  }

  /**
   * US003: Update Customer Details
   */
  updateCustomer(customerId: number, customer: CustomerUpdateRequest): Observable<any> {
    const headers = this.authService.getAuthHeaders();
    return this.http.put(`${this.apiUrl}/update/${customerId}`, customer, { headers });
  }

  /**
   * US004: Get Customer Order Details
   */
  getCustomerOrderDetails(customerId: number): Observable<any> {
    const headers = this.authService.getAuthHeaders();
    return this.http.get(`${this.apiUrl}/${customerId}/orders`, { headers });
  }

  /**
   * US005: Search Customer by Name (Admin only)
   */
  searchCustomersByName(name: string): Observable<any> {
    const headers = this.authService.getAuthHeaders();
    return this.http.get(`${this.apiUrl}/search?name=${encodeURIComponent(name)}`, { headers });
  }

  /**
   * Get customer by ID
   */
  getCustomerById(customerId: number): Observable<any> {
    const headers = this.authService.getAuthHeaders();
    return this.http.get(`${this.apiUrl}/${customerId}`, { headers });
  }

  /**
   * Get all customers (Admin only)
   */
  getAllCustomers(): Observable<any> {
    const headers = this.authService.getAuthHeaders();
    return this.http.get(`${this.apiUrl}`, { headers });
  }

  /**
   * Delete customer (Admin only)
   */
  deleteCustomer(customerId: number): Observable<any> {
    const headers = this.authService.getAuthHeaders();
    return this.http.delete(`${this.apiUrl}/${customerId}`, { headers });
  }

  /**
   * Validate customer input
   */
  validateCustomerData(customer: CustomerRegistrationRequest | CustomerUpdateRequest): string[] {
    const errors: string[] = [];

    // Validate full name
    if (!customer.fullName || customer.fullName.trim().length < 2) {
      errors.push('Full name must be at least 2 characters long');
    }
    if (customer.fullName && customer.fullName.length > 100) {
      errors.push('Full name cannot exceed 100 characters');
    }

    // Validate email
    const emailPattern = /^[a-zA-Z0-9_+&*-]+(?:\.[a-zA-Z0-9_+&*-]+)*@(?:[a-zA-Z0-9-]+\.)+[a-zA-Z]{2,7}$/;
    if (!customer.email || !emailPattern.test(customer.email)) {
      errors.push('Please provide a valid email address');
    }

    // Validate password (for registration or password updates)
    if ('password' in customer && customer.password) {
      const passwordPattern = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/;
      if (!passwordPattern.test(customer.password)) {
        errors.push('Password must be at least 8 characters long and contain at least one uppercase letter, one lowercase letter, one digit, and one special character');
      }
    }

    // Validate address
    if (!customer.address || customer.address.trim().length === 0) {
      errors.push('Address is required');
    }

    // Validate contact number
    const phonePattern = /^\d{10}$/;
    if (!customer.contactNumber || !phonePattern.test(customer.contactNumber)) {
      errors.push('Contact number must be exactly 10 digits');
    }

    return errors;
  }
}
