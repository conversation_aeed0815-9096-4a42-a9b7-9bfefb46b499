import { Routes } from '@angular/router';
import { AuthGuard, AdminGuard, CustomerGuard } from './guards/auth.guard';

export const routes: Routes = [
  {
    path: '',
    redirectTo: '/login',
    pathMatch: 'full'
  },
  {
    path: 'login',
    loadComponent: () => import('./components/login/login.component').then(m => m.LoginComponent)
  },
  {
    path: 'register',
    loadComponent: () => import('./components/customer-registration/customer-registration.component').then(m => m.CustomerRegistrationComponent)
  },
  {
    path: 'dashboard',
    loadComponent: () => import('./components/dashboard/dashboard.component').then(m => m.DashboardComponent),
    canActivate: [AuthGuard]
  },
  {
    path: 'customer-update',
    loadComponent: () => import('./components/customer-update/customer-update.component').then(m => m.CustomerUpdateComponent),
    canActivate: [AuthGuard]
  },
  {
    path: 'customer-search',
    loadComponent: () => import('./components/customer-search/customer-search.component').then(m => m.CustomerSearchComponent),
    canActivate: [AdminGuard]
  },
  {
    path: 'product-search',
    loadComponent: () => import('./components/product-search/product-search.component').then(m => m.ProductSearchComponent),
    canActivate: [AuthGuard]
  },
  {
    path: 'product-registration',
    loadComponent: () => import('./components/product-registration/product-registration.component').then(m => m.ProductRegistrationComponent),
    canActivate: [AdminGuard]
  },
  {
    path: 'product-update',
    loadComponent: () => import('./components/product-update/product-update.component').then(m => m.ProductUpdateComponent),
    canActivate: [AdminGuard]
  },
  {
    path: 'order-history',
    loadComponent: () => import('./components/order-history/order-history.component').then(m => m.OrderHistoryComponent),
    canActivate: [AuthGuard]
  },
  {
    path: 'admin-panel',
    loadComponent: () => import('./components/admin-panel/admin-panel.component').then(m => m.AdminPanelComponent),
    canActivate: [AdminGuard]
  },
  {
    path: '**',
    redirectTo: '/login'
  }
];
