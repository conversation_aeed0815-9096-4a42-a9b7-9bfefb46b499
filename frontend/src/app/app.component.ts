import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterOutlet, Router, NavigationEnd } from '@angular/router';
import { MatToolbarModule } from '@angular/material/toolbar';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatMenuModule } from '@angular/material/menu';
import { MatSnackBarModule } from '@angular/material/snack-bar';
import { AuthService } from './services/auth.service';
import { AuthUser } from './models/auth.model';
import { filter } from 'rxjs/operators';

@Component({
  selector: 'app-root',
  standalone: true,
  imports: [
    CommonModule,
    RouterOutlet,
    MatToolbarModule,
    MatButtonModule,
    MatIconModule,
    MatMenuModule,
    MatSnackBarModule
  ],
  template: `
    <div class="app-container">
      <!-- Navigation Bar -->
      <mat-toolbar color="primary" *ngIf="showNavbar">
        <span class="app-title">
          <mat-icon>shopping_cart</mat-icon>
          Online Grocery System
        </span>
        
        <span class="spacer"></span>
        
        <!-- Navigation Menu -->
        <div class="nav-menu" *ngIf="currentUser">
          <button mat-button routerLink="/dashboard">
            <mat-icon>dashboard</mat-icon>
            Dashboard
          </button>
          
          <!-- Customer Menu -->
          <ng-container *ngIf="authService.isCustomer()">
            <button mat-button routerLink="/product-search">
              <mat-icon>search</mat-icon>
              Search Products
            </button>
            <button mat-button routerLink="/order-history">
              <mat-icon>history</mat-icon>
              My Orders
            </button>
            <button mat-button routerLink="/customer-update">
              <mat-icon>person</mat-icon>
              Update Profile
            </button>
          </ng-container>
          
          <!-- Admin Menu -->
          <ng-container *ngIf="authService.isAdmin()">
            <button mat-button [matMenuTriggerFor]="customerMenu">
              <mat-icon>people</mat-icon>
              Customers
            </button>
            <mat-menu #customerMenu="matMenu">
              <button mat-menu-item routerLink="/customer-search">
                <mat-icon>search</mat-icon>
                Search Customers
              </button>
            </mat-menu>
            
            <button mat-button [matMenuTriggerFor]="productMenu">
              <mat-icon>inventory</mat-icon>
              Products
            </button>
            <mat-menu #productMenu="matMenu">
              <button mat-menu-item routerLink="/product-search">
                <mat-icon>search</mat-icon>
                Search Products
              </button>
              <button mat-menu-item routerLink="/product-registration">
                <mat-icon>add</mat-icon>
                Register Product
              </button>
              <button mat-menu-item routerLink="/product-update">
                <mat-icon>edit</mat-icon>
                Update Product
              </button>
            </mat-menu>
            
            <button mat-button routerLink="/admin-panel">
              <mat-icon>admin_panel_settings</mat-icon>
              Admin Panel
            </button>
          </ng-container>
          
          <!-- User Menu -->
          <button mat-button [matMenuTriggerFor]="userMenu">
            <mat-icon>account_circle</mat-icon>
            {{ currentUser.fullName || currentUser.username }}
          </button>
          <mat-menu #userMenu="matMenu">
            <div mat-menu-item disabled>
              <strong>{{ currentUser.role }}</strong>
            </div>
            <mat-divider></mat-divider>
            <button mat-menu-item (click)="logout()">
              <mat-icon>logout</mat-icon>
              Logout
            </button>
          </mat-menu>
        </div>
      </mat-toolbar>
      
      <!-- Main Content -->
      <main class="main-content" [class.with-navbar]="showNavbar">
        <router-outlet></router-outlet>
      </main>
    </div>
  `,
  styles: [`
    .app-container {
      height: 100vh;
      display: flex;
      flex-direction: column;
    }
    
    .app-title {
      display: flex;
      align-items: center;
      gap: 8px;
      font-weight: 500;
    }
    
    .spacer {
      flex: 1 1 auto;
    }
    
    .nav-menu {
      display: flex;
      align-items: center;
      gap: 8px;
    }
    
    .nav-menu button {
      display: flex;
      align-items: center;
      gap: 4px;
    }
    
    .main-content {
      flex: 1;
      overflow-y: auto;
      background-color: #f5f5f5;
    }
    
    .main-content.with-navbar {
      padding-top: 0;
    }
    
    .main-content:not(.with-navbar) {
      padding-top: 0;
    }
    
    @media (max-width: 768px) {
      .nav-menu {
        flex-direction: column;
        gap: 4px;
      }
      
      .nav-menu button {
        font-size: 12px;
        padding: 4px 8px;
      }
    }
  `]
})
export class AppComponent implements OnInit {
  title = 'Online Grocery System';
  currentUser: AuthUser | null = null;
  showNavbar = false;

  constructor(
    public authService: AuthService,
    private router: Router
  ) {}

  ngOnInit() {
    // Subscribe to current user changes
    this.authService.currentUser$.subscribe(user => {
      this.currentUser = user;
      this.updateNavbarVisibility();
    });

    // Subscribe to route changes to update navbar visibility
    this.router.events.pipe(
      filter(event => event instanceof NavigationEnd)
    ).subscribe(() => {
      this.updateNavbarVisibility();
    });
  }

  private updateNavbarVisibility() {
    const currentRoute = this.router.url;
    const publicRoutes = ['/login', '/register'];
    this.showNavbar = this.currentUser !== null && !publicRoutes.includes(currentRoute);
  }

  logout() {
    this.authService.logout().subscribe({
      next: () => {
        this.router.navigate(['/login']);
      },
      error: (error) => {
        console.error('Logout error:', error);
        // Force logout even if API call fails
        this.router.navigate(['/login']);
      }
    });
  }
}
