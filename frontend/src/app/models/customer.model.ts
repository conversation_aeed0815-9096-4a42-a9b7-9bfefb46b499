export interface Customer {
  customerId?: number;
  fullName: string;
  email: string;
  password?: string;
  address: string;
  contactNumber: string;
  createdAt?: string;
  updatedAt?: string;
}

export interface CustomerRegistrationRequest {
  fullName: string;
  email: string;
  password: string;
  address: string;
  contactNumber: string;
}

export interface CustomerUpdateRequest {
  fullName: string;
  email: string;
  password?: string;
  address: string;
  contactNumber: string;
}
