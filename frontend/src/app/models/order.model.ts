export interface Order {
  orderId?: number;
  customerId: number;
  productId: number;
  orderDate?: string;
  orderAmount: number;
  quantity: number;
  status?: string;
  customerName?: string;
  productName?: string;
}

export interface CreateOrderRequest {
  customerId: number;
  productId: number;
  quantity: number;
}

export interface OrderUpdateRequest {
  customerId: number;
  productId: number;
  orderAmount: number;
  quantity: number;
  status: string;
}
