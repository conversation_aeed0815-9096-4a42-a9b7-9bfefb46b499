export interface LoginRequest {
  username: string;
  password: string;
}

export interface LoginResponse {
  token: string;
  role: string;
  userId: number;
  username: string;
  fullName?: string;
  message: string;
}

export interface AuthUser {
  userId: number;
  username: string;
  role: string;
  fullName?: string;
  token: string;
}

export interface ApiResponse<T> {
  message: string;
  data?: T;
  error?: string;
}
