export interface Product {
  productId?: number;
  productName: string;
  price: number;
  quantity: number;
  reserved?: number;
  customerId?: number;
  createdAt?: string;
  updatedAt?: string;
  availableQuantity?: number;
  isAvailable?: boolean;
}

export interface ProductRegistrationRequest {
  productName: string;
  price: number;
  quantity: number;
  reserved?: number;
  customerId?: number;
}

export interface ProductUpdateRequest {
  productName: string;
  price: number;
  quantity: number;
  reserved?: number;
  customerId?: number;
}
