import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatTableModule } from '@angular/material/table';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { CustomerService } from '../../services/customer.service';
import { OrderService } from '../../services/order.service';
import { AuthService } from '../../services/auth.service';
import { Order } from '../../models/order.model';

@Component({
  selector: 'app-order-history',
  standalone: true,
  imports: [
    CommonModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatTableModule,
    MatSnackBarModule
  ],
  template: `
    <div class="order-history-container">
      <mat-card class="header-card">
        <mat-card-header>
          <mat-card-title>
            <mat-icon>history</mat-icon>
            Order History
          </mat-card-title>
          <mat-card-subtitle>View your past orders and track current ones</mat-card-subtitle>
        </mat-card-header>
        
        <mat-card-actions>
          <button mat-raised-button color="primary" (click)="loadOrderHistory()" [disabled]="isLoading">
            <mat-icon *ngIf="isLoading">hourglass_empty</mat-icon>
            <mat-icon *ngIf="!isLoading">refresh</mat-icon>
            {{ isLoading ? 'Loading...' : 'Refresh Orders' }}
          </button>
        </mat-card-actions>
      </mat-card>

      <!-- Order History Table -->
      <mat-card *ngIf="orders.length > 0" class="orders-card">
        <mat-card-header>
          <mat-card-title>
            <mat-icon>shopping_cart</mat-icon>
            Your Orders ({{ orders.length }} total)
          </mat-card-title>
        </mat-card-header>
        
        <mat-card-content>
          <div class="table-container">
            <table mat-table [dataSource]="orders" class="orders-table">
              <!-- Order ID Column -->
              <ng-container matColumnDef="orderId">
                <th mat-header-cell *matHeaderCellDef>Order ID</th>
                <td mat-cell *matCellDef="let order">{{ order.orderId }}</td>
              </ng-container>

              <!-- Product Name Column -->
              <ng-container matColumnDef="productName">
                <th mat-header-cell *matHeaderCellDef>Product</th>
                <td mat-cell *matCellDef="let order">{{ order.productName }}</td>
              </ng-container>

              <!-- Order Date Column -->
              <ng-container matColumnDef="orderDate">
                <th mat-header-cell *matHeaderCellDef>Order Date</th>
                <td mat-cell *matCellDef="let order">{{ formatOrderDate(order.orderDate) }}</td>
              </ng-container>

              <!-- Quantity Column -->
              <ng-container matColumnDef="quantity">
                <th mat-header-cell *matHeaderCellDef>Quantity</th>
                <td mat-cell *matCellDef="let order">{{ order.quantity }}</td>
              </ng-container>

              <!-- Order Amount Column -->
              <ng-container matColumnDef="orderAmount">
                <th mat-header-cell *matHeaderCellDef>Amount</th>
                <td mat-cell *matCellDef="let order">{{ formatOrderAmount(order.orderAmount) }}</td>
              </ng-container>

              <!-- Status Column -->
              <ng-container matColumnDef="status">
                <th mat-header-cell *matHeaderCellDef>Status</th>
                <td mat-cell *matCellDef="let order">
                  <span [style.color]="getOrderStatusColor(order.status)" class="status-badge">
                    {{ getOrderStatusText(order.status) }}
                  </span>
                </td>
              </ng-container>

              <!-- Actions Column -->
              <ng-container matColumnDef="actions">
                <th mat-header-cell *matHeaderCellDef>Actions</th>
                <td mat-cell *matCellDef="let order">
                  <button mat-button 
                          color="warn" 
                          size="small"
                          [disabled]="!canCancelOrder(order.status) || isCancelling"
                          (click)="cancelOrder(order)">
                    <mat-icon>cancel</mat-icon>
                    Cancel
                  </button>
                </td>
              </ng-container>

              <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
              <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
            </table>
          </div>
        </mat-card-content>
      </mat-card>

      <!-- No Orders Message -->
      <mat-card *ngIf="hasLoaded && orders.length === 0" class="no-orders-card">
        <mat-card-content>
          <div class="no-orders">
            <mat-icon>shopping_cart_outlined</mat-icon>
            <h3>No Orders Found</h3>
            <p>You haven't placed any orders yet. Start shopping to see your order history here.</p>
            <button mat-raised-button color="primary" routerLink="/product-search">
              <mat-icon>search</mat-icon>
              Browse Products
            </button>
          </div>
        </mat-card-content>
      </mat-card>

      <!-- Loading State -->
      <mat-card *ngIf="isLoading && !hasLoaded" class="loading-card">
        <mat-card-content>
          <div class="loading">
            <mat-icon>hourglass_empty</mat-icon>
            <p>Loading your order history...</p>
          </div>
        </mat-card-content>
      </mat-card>
    </div>
  `,
  styles: [`
    .order-history-container {
      padding: 24px;
      max-width: 1200px;
      margin: 0 auto;
    }
    
    .header-card, .orders-card, .no-orders-card, .loading-card {
      margin-bottom: 24px;
    }
    
    .table-container {
      overflow-x: auto;
      margin-top: 16px;
    }
    
    .orders-table {
      width: 100%;
      min-width: 800px;
    }
    
    .orders-table th {
      font-weight: 600;
      color: #333;
    }
    
    .status-badge {
      font-weight: 500;
      padding: 4px 8px;
      border-radius: 4px;
      font-size: 12px;
    }
    
    .no-orders, .loading {
      text-align: center;
      padding: 48px 24px;
      color: #666;
    }
    
    .no-orders mat-icon, .loading mat-icon {
      font-size: 64px;
      width: 64px;
      height: 64px;
      margin-bottom: 16px;
      color: #ccc;
    }
    
    .no-orders h3 {
      margin: 16px 0 8px 0;
      color: #333;
    }
    
    .no-orders p {
      margin: 0 0 24px 0;
    }
    
    @media (max-width: 768px) {
      .order-history-container {
        padding: 16px;
      }
    }
  `]
})
export class OrderHistoryComponent implements OnInit {
  orders: Order[] = [];
  isLoading = false;
  isCancelling = false;
  hasLoaded = false;
  customerId: number | null = null;
  displayedColumns: string[] = ['orderId', 'productName', 'orderDate', 'quantity', 'orderAmount', 'status', 'actions'];

  constructor(
    private customerService: CustomerService,
    private orderService: OrderService,
    private authService: AuthService,
    private snackBar: MatSnackBar
  ) {}

  ngOnInit() {
    this.customerId = this.authService.getUserId();
    if (this.customerId) {
      this.loadOrderHistory();
    }
  }

  loadOrderHistory() {
    if (!this.customerId) return;
    
    this.isLoading = true;

    this.customerService.getCustomerOrderDetails(this.customerId).subscribe({
      next: (response) => {
        this.isLoading = false;
        this.hasLoaded = true;
        this.orders = response.orders || [];
        
        if (this.orders.length > 0) {
          this.snackBar.open(`Loaded ${this.orders.length} order(s)`, 'Close', {
            duration: 3000,
            panelClass: ['success-snackbar']
          });
        }
      },
      error: (error) => {
        this.isLoading = false;
        this.hasLoaded = true;
        this.orders = [];
        const errorMessage = error.error?.error || 'Failed to load order history. Please try again.';
        this.snackBar.open(errorMessage, 'Close', {
          duration: 5000,
          panelClass: ['error-snackbar']
        });
      }
    });
  }

  cancelOrder(order: Order) {
    if (!this.canCancelOrder(order.status)) {
      this.snackBar.open('This order cannot be cancelled', 'Close', {
        duration: 3000,
        panelClass: ['error-snackbar']
      });
      return;
    }

    this.isCancelling = true;

    this.orderService.cancelOrder(order.orderId!).subscribe({
      next: (response) => {
        this.isCancelling = false;
        this.snackBar.open('Order cancelled successfully', 'Close', {
          duration: 3000,
          panelClass: ['success-snackbar']
        });
        
        // Refresh order history
        this.loadOrderHistory();
      },
      error: (error) => {
        this.isCancelling = false;
        const errorMessage = error.error?.error || 'Failed to cancel order. Please try again.';
        this.snackBar.open(errorMessage, 'Close', {
          duration: 5000,
          panelClass: ['error-snackbar']
        });
      }
    });
  }

  formatOrderDate(dateString: string): string {
    return this.orderService.formatOrderDate(dateString);
  }

  formatOrderAmount(amount: number): string {
    return this.orderService.formatOrderAmount(amount);
  }

  getOrderStatusColor(status: string): string {
    return this.orderService.getOrderStatusColor(status);
  }

  getOrderStatusText(status: string): string {
    return this.orderService.getOrderStatusText(status);
  }

  canCancelOrder(status: string): boolean {
    return this.orderService.canCancelOrder(status);
  }
}
