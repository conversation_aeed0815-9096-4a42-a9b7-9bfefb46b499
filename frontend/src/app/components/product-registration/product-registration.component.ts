import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';
import { Router } from '@angular/router';
import { MatCardModule } from '@angular/material/card';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { ProductService } from '../../services/product.service';
import { ProductRegistrationRequest } from '../../models/product.model';

@Component({
  selector: 'app-product-registration',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatCardModule,
    MatFormFieldModule,
    MatInputModule,
    MatButtonModule,
    MatIconModule,
    MatSnackBarModule
  ],
  template: `
    <div class="registration-container">
      <mat-card class="registration-card">
        <mat-card-header>
          <mat-card-title>
            <mat-icon>add_box</mat-icon>
            Product Registration (Admin Only)
          </mat-card-title>
          <mat-card-subtitle>Add new products to the inventory</mat-card-subtitle>
        </mat-card-header>
        
        <mat-card-content>
          <form [formGroup]="registrationForm" (ngSubmit)="onSubmit()" class="registration-form">
            <mat-form-field appearance="outline" class="full-width">
              <mat-label>Product Name</mat-label>
              <input matInput 
                     formControlName="productName" 
                     placeholder="Enter product name">
              <mat-icon matSuffix>inventory</mat-icon>
              <mat-error *ngIf="registrationForm.get('productName')?.hasError('required')">
                Product name is required
              </mat-error>
              <mat-error *ngIf="registrationForm.get('productName')?.hasError('minlength')">
                Product name must be at least 2 characters long
              </mat-error>
            </mat-form-field>
            
            <mat-form-field appearance="outline" class="full-width">
              <mat-label>Price</mat-label>
              <input matInput 
                     type="number"
                     step="0.01"
                     min="0"
                     formControlName="price" 
                     placeholder="Enter product price">
              <span matPrefix>$&nbsp;</span>
              <mat-icon matSuffix>attach_money</mat-icon>
              <mat-error *ngIf="registrationForm.get('price')?.hasError('required')">
                Price is required
              </mat-error>
              <mat-error *ngIf="registrationForm.get('price')?.hasError('min')">
                Price must be greater than 0
              </mat-error>
            </mat-form-field>
            
            <mat-form-field appearance="outline" class="full-width">
              <mat-label>Quantity</mat-label>
              <input matInput 
                     type="number"
                     min="0"
                     formControlName="quantity" 
                     placeholder="Enter available quantity">
              <mat-icon matSuffix>inventory_2</mat-icon>
              <mat-error *ngIf="registrationForm.get('quantity')?.hasError('required')">
                Quantity is required
              </mat-error>
              <mat-error *ngIf="registrationForm.get('quantity')?.hasError('min')">
                Quantity cannot be negative
              </mat-error>
            </mat-form-field>
            
            <mat-form-field appearance="outline" class="full-width">
              <mat-label>Reserved Quantity (Optional)</mat-label>
              <input matInput 
                     type="number"
                     min="0"
                     formControlName="reserved" 
                     placeholder="Enter reserved quantity (default: 0)">
              <mat-icon matSuffix>lock</mat-icon>
              <mat-error *ngIf="registrationForm.get('reserved')?.hasError('min')">
                Reserved quantity cannot be negative
              </mat-error>
            </mat-form-field>
            
            <button mat-raised-button 
                    color="primary" 
                    type="submit" 
                    class="register-button full-width"
                    [disabled]="registrationForm.invalid || isLoading">
              <mat-icon *ngIf="isLoading">hourglass_empty</mat-icon>
              <mat-icon *ngIf="!isLoading">add_box</mat-icon>
              {{ isLoading ? 'Registering...' : 'Register Product' }}
            </button>
          </form>
        </mat-card-content>
        
        <mat-card-actions class="registration-actions">
          <button mat-button 
                  color="accent" 
                  (click)="goToDashboard()"
                  class="full-width">
            <mat-icon>dashboard</mat-icon>
            Back to Dashboard
          </button>
        </mat-card-actions>
      </mat-card>
    </div>
  `,
  styles: [`
    .registration-container {
      padding: 24px;
      max-width: 600px;
      margin: 0 auto;
    }
    
    .registration-form {
      display: flex;
      flex-direction: column;
      gap: 16px;
      margin-top: 16px;
    }
    
    .full-width {
      width: 100%;
    }
    
    .register-button {
      height: 48px;
      font-size: 16px;
      margin-top: 8px;
    }
    
    .registration-actions {
      padding: 16px;
      display: flex;
      justify-content: center;
    }
    
    @media (max-width: 480px) {
      .registration-container {
        padding: 16px;
      }
    }
  `]
})
export class ProductRegistrationComponent implements OnInit {
  registrationForm: FormGroup;
  isLoading = false;

  constructor(
    private formBuilder: FormBuilder,
    private productService: ProductService,
    private router: Router,
    private snackBar: MatSnackBar
  ) {
    this.registrationForm = this.formBuilder.group({
      productName: ['', [Validators.required, Validators.minLength(2), Validators.maxLength(100)]],
      price: ['', [Validators.required, Validators.min(0.01)]],
      quantity: ['', [Validators.required, Validators.min(0)]],
      reserved: [0, [Validators.min(0)]]
    });
  }

  ngOnInit() {
    // Component initialization
  }

  onSubmit() {
    if (this.registrationForm.valid) {
      this.isLoading = true;
      
      const registrationRequest: ProductRegistrationRequest = {
        productName: this.registrationForm.value.productName,
        price: parseFloat(this.registrationForm.value.price),
        quantity: parseInt(this.registrationForm.value.quantity),
        reserved: parseInt(this.registrationForm.value.reserved) || 0
      };

      // Validate using service
      const validationErrors = this.productService.validateProductData(registrationRequest);
      if (validationErrors.length > 0) {
        this.isLoading = false;
        this.snackBar.open(validationErrors.join(', '), 'Close', {
          duration: 5000,
          panelClass: ['error-snackbar']
        });
        return;
      }

      this.productService.registerProduct(registrationRequest).subscribe({
        next: (response) => {
          this.isLoading = false;
          this.snackBar.open(response.message, 'Close', {
            duration: 3000,
            panelClass: ['success-snackbar']
          });
          
          // Reset form for next product
          this.registrationForm.reset();
          this.registrationForm.patchValue({ reserved: 0 });
        },
        error: (error) => {
          this.isLoading = false;
          const errorMessage = error.error?.error || 'Product registration failed. Please try again.';
          this.snackBar.open(errorMessage, 'Close', {
            duration: 5000,
            panelClass: ['error-snackbar']
          });
        }
      });
    }
  }

  goToDashboard() {
    this.router.navigate(['/dashboard']);
  }
}
