import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router } from '@angular/router';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatTableModule } from '@angular/material/table';
import { MatTabsModule } from '@angular/material/tabs';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { CustomerService } from '../../services/customer.service';
import { ProductService } from '../../services/product.service';
import { OrderService } from '../../services/order.service';
import { Customer } from '../../models/customer.model';
import { Product } from '../../models/product.model';
import { Order } from '../../models/order.model';

@Component({
  selector: 'app-admin-panel',
  standalone: true,
  imports: [
    CommonModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatTableModule,
    MatTabsModule,
    MatSnackBarModule
  ],
  template: `
    <div class="admin-panel-container">
      <mat-card class="header-card">
        <mat-card-header>
          <mat-card-title>
            <mat-icon>admin_panel_settings</mat-icon>
            Administrator Panel
          </mat-card-title>
          <mat-card-subtitle>Complete system overview and management</mat-card-subtitle>
        </mat-card-header>
        
        <mat-card-actions>
          <button mat-raised-button color="primary" (click)="refreshAllData()" [disabled]="isLoading">
            <mat-icon *ngIf="isLoading">hourglass_empty</mat-icon>
            <mat-icon *ngIf="!isLoading">refresh</mat-icon>
            {{ isLoading ? 'Loading...' : 'Refresh Data' }}
          </button>
        </mat-card-actions>
      </mat-card>

      <!-- Statistics Cards -->
      <div class="stats-grid">
        <mat-card class="stat-card">
          <mat-card-content>
            <div class="stat-content">
              <mat-icon class="stat-icon customers">people</mat-icon>
              <div class="stat-info">
                <h3>{{ customers.length }}</h3>
                <p>Total Customers</p>
              </div>
            </div>
          </mat-card-content>
        </mat-card>

        <mat-card class="stat-card">
          <mat-card-content>
            <div class="stat-content">
              <mat-icon class="stat-icon products">inventory</mat-icon>
              <div class="stat-info">
                <h3>{{ products.length }}</h3>
                <p>Total Products</p>
              </div>
            </div>
          </mat-card-content>
        </mat-card>

        <mat-card class="stat-card">
          <mat-card-content>
            <div class="stat-content">
              <mat-icon class="stat-icon orders">shopping_cart</mat-icon>
              <div class="stat-info">
                <h3>{{ orders.length }}</h3>
                <p>Total Orders</p>
              </div>
            </div>
          </mat-card-content>
        </mat-card>

        <mat-card class="stat-card">
          <mat-card-content>
            <div class="stat-content">
              <mat-icon class="stat-icon revenue">attach_money</mat-icon>
              <div class="stat-info">
                <h3>{{ getTotalRevenue() | currency }}</h3>
                <p>Total Revenue</p>
              </div>
            </div>
          </mat-card-content>
        </mat-card>
      </div>

      <!-- Tabbed Data View -->
      <mat-card class="data-card">
        <mat-tab-group>
          <!-- Customers Tab -->
          <mat-tab label="Customers">
            <div class="tab-content">
              <div class="tab-header">
                <h3>Customer Management</h3>
                <button mat-raised-button color="primary" routerLink="/customer-search">
                  <mat-icon>search</mat-icon>
                  Search Customers
                </button>
              </div>
              
              <div class="table-container" *ngIf="customers.length > 0">
                <table mat-table [dataSource]="customers.slice(0, 10)" class="data-table">
                  <ng-container matColumnDef="customerId">
                    <th mat-header-cell *matHeaderCellDef>ID</th>
                    <td mat-cell *matCellDef="let customer">{{ customer.customerId }}</td>
                  </ng-container>

                  <ng-container matColumnDef="fullName">
                    <th mat-header-cell *matHeaderCellDef>Name</th>
                    <td mat-cell *matCellDef="let customer">{{ customer.fullName }}</td>
                  </ng-container>

                  <ng-container matColumnDef="email">
                    <th mat-header-cell *matHeaderCellDef>Email</th>
                    <td mat-cell *matCellDef="let customer">{{ customer.email }}</td>
                  </ng-container>

                  <ng-container matColumnDef="contactNumber">
                    <th mat-header-cell *matHeaderCellDef>Contact</th>
                    <td mat-cell *matCellDef="let customer">{{ customer.contactNumber }}</td>
                  </ng-container>

                  <tr mat-header-row *matHeaderRowDef="customerColumns"></tr>
                  <tr mat-row *matRowDef="let row; columns: customerColumns;"></tr>
                </table>
                <p class="table-footer" *ngIf="customers.length > 10">
                  Showing 10 of {{ customers.length }} customers. 
                  <a routerLink="/customer-search">View all customers</a>
                </p>
              </div>
            </div>
          </mat-tab>

          <!-- Products Tab -->
          <mat-tab label="Products">
            <div class="tab-content">
              <div class="tab-header">
                <h3>Product Management</h3>
                <div class="tab-actions">
                  <button mat-raised-button color="primary" routerLink="/product-registration">
                    <mat-icon>add</mat-icon>
                    Add Product
                  </button>
                  <button mat-raised-button color="accent" routerLink="/product-update">
                    <mat-icon>edit</mat-icon>
                    Update Products
                  </button>
                </div>
              </div>
              
              <div class="table-container" *ngIf="products.length > 0">
                <table mat-table [dataSource]="products.slice(0, 10)" class="data-table">
                  <ng-container matColumnDef="productId">
                    <th mat-header-cell *matHeaderCellDef>ID</th>
                    <td mat-cell *matCellDef="let product">{{ product.productId }}</td>
                  </ng-container>

                  <ng-container matColumnDef="productName">
                    <th mat-header-cell *matHeaderCellDef>Name</th>
                    <td mat-cell *matCellDef="let product">{{ product.productName }}</td>
                  </ng-container>

                  <ng-container matColumnDef="price">
                    <th mat-header-cell *matHeaderCellDef>Price</th>
                    <td mat-cell *matCellDef="let product">{{ formatPrice(product.price) }}</td>
                  </ng-container>

                  <ng-container matColumnDef="quantity">
                    <th mat-header-cell *matHeaderCellDef>Stock</th>
                    <td mat-cell *matCellDef="let product">{{ product.quantity }}</td>
                  </ng-container>

                  <tr mat-header-row *matHeaderRowDef="productColumns"></tr>
                  <tr mat-row *matRowDef="let row; columns: productColumns;"></tr>
                </table>
                <p class="table-footer" *ngIf="products.length > 10">
                  Showing 10 of {{ products.length }} products. 
                  <a routerLink="/product-search">View all products</a>
                </p>
              </div>
            </div>
          </mat-tab>

          <!-- Orders Tab -->
          <mat-tab label="Orders">
            <div class="tab-content">
              <div class="tab-header">
                <h3>Order Management</h3>
              </div>
              
              <div class="table-container" *ngIf="orders.length > 0">
                <table mat-table [dataSource]="orders.slice(0, 10)" class="data-table">
                  <ng-container matColumnDef="orderId">
                    <th mat-header-cell *matHeaderCellDef>Order ID</th>
                    <td mat-cell *matCellDef="let order">{{ order.orderId }}</td>
                  </ng-container>

                  <ng-container matColumnDef="customerName">
                    <th mat-header-cell *matHeaderCellDef>Customer</th>
                    <td mat-cell *matCellDef="let order">{{ order.customerName }}</td>
                  </ng-container>

                  <ng-container matColumnDef="productName">
                    <th mat-header-cell *matHeaderCellDef>Product</th>
                    <td mat-cell *matCellDef="let order">{{ order.productName }}</td>
                  </ng-container>

                  <ng-container matColumnDef="orderAmount">
                    <th mat-header-cell *matHeaderCellDef>Amount</th>
                    <td mat-cell *matCellDef="let order">{{ formatOrderAmount(order.orderAmount) }}</td>
                  </ng-container>

                  <ng-container matColumnDef="status">
                    <th mat-header-cell *matHeaderCellDef>Status</th>
                    <td mat-cell *matCellDef="let order">
                      <span [style.color]="getOrderStatusColor(order.status)">
                        {{ getOrderStatusText(order.status) }}
                      </span>
                    </td>
                  </ng-container>

                  <tr mat-header-row *matHeaderRowDef="orderColumns"></tr>
                  <tr mat-row *matRowDef="let row; columns: orderColumns;"></tr>
                </table>
                <p class="table-footer" *ngIf="orders.length > 10">
                  Showing 10 of {{ orders.length }} orders.
                </p>
              </div>
            </div>
          </mat-tab>
        </mat-tab-group>
      </mat-card>
    </div>
  `,
  styles: [`
    .admin-panel-container {
      padding: 24px;
      max-width: 1400px;
      margin: 0 auto;
    }
    
    .header-card {
      margin-bottom: 24px;
    }
    
    .stats-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: 24px;
      margin-bottom: 24px;
    }
    
    .stat-card {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
    }
    
    .stat-content {
      display: flex;
      align-items: center;
      gap: 16px;
    }
    
    .stat-icon {
      font-size: 48px;
      width: 48px;
      height: 48px;
      opacity: 0.8;
    }
    
    .stat-info h3 {
      margin: 0;
      font-size: 2rem;
      font-weight: 600;
    }
    
    .stat-info p {
      margin: 4px 0 0 0;
      opacity: 0.9;
    }
    
    .data-card {
      margin-bottom: 24px;
    }
    
    .tab-content {
      padding: 24px;
    }
    
    .tab-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 24px;
    }
    
    .tab-header h3 {
      margin: 0;
      color: #333;
    }
    
    .tab-actions {
      display: flex;
      gap: 16px;
    }
    
    .table-container {
      overflow-x: auto;
    }
    
    .data-table {
      width: 100%;
      min-width: 600px;
    }
    
    .data-table th {
      font-weight: 600;
      color: #333;
    }
    
    .table-footer {
      margin-top: 16px;
      text-align: center;
      color: #666;
    }
    
    .table-footer a {
      color: #1976d2;
      text-decoration: none;
    }
    
    .table-footer a:hover {
      text-decoration: underline;
    }
    
    @media (max-width: 768px) {
      .admin-panel-container {
        padding: 16px;
      }
      
      .stats-grid {
        grid-template-columns: 1fr;
        gap: 16px;
      }
      
      .tab-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 16px;
      }
      
      .tab-actions {
        flex-direction: column;
        width: 100%;
      }
      
      .tab-actions button {
        width: 100%;
      }
    }
  `]
})
export class AdminPanelComponent implements OnInit {
  customers: Customer[] = [];
  products: Product[] = [];
  orders: Order[] = [];
  isLoading = false;

  customerColumns: string[] = ['customerId', 'fullName', 'email', 'contactNumber'];
  productColumns: string[] = ['productId', 'productName', 'price', 'quantity'];
  orderColumns: string[] = ['orderId', 'customerName', 'productName', 'orderAmount', 'status'];

  constructor(
    private customerService: CustomerService,
    private productService: ProductService,
    private orderService: OrderService,
    private router: Router,
    private snackBar: MatSnackBar
  ) {}

  ngOnInit() {
    this.refreshAllData();
  }

  refreshAllData() {
    this.isLoading = true;
    
    // Load all data concurrently
    Promise.all([
      this.loadCustomers(),
      this.loadProducts(),
      this.loadOrders()
    ]).then(() => {
      this.isLoading = false;
      this.snackBar.open('Data refreshed successfully', 'Close', {
        duration: 3000,
        panelClass: ['success-snackbar']
      });
    }).catch(() => {
      this.isLoading = false;
      this.snackBar.open('Failed to refresh some data', 'Close', {
        duration: 5000,
        panelClass: ['error-snackbar']
      });
    });
  }

  private loadCustomers(): Promise<void> {
    return new Promise((resolve, reject) => {
      this.customerService.getAllCustomers().subscribe({
        next: (response) => {
          this.customers = response.customers || [];
          resolve();
        },
        error: () => {
          this.customers = [];
          reject();
        }
      });
    });
  }

  private loadProducts(): Promise<void> {
    return new Promise((resolve, reject) => {
      this.productService.getAllProducts().subscribe({
        next: (response) => {
          this.products = response.products || [];
          resolve();
        },
        error: () => {
          this.products = [];
          reject();
        }
      });
    });
  }

  private loadOrders(): Promise<void> {
    return new Promise((resolve, reject) => {
      this.orderService.getAllOrders().subscribe({
        next: (response) => {
          this.orders = response.orders || [];
          resolve();
        },
        error: () => {
          this.orders = [];
          reject();
        }
      });
    });
  }

  getTotalRevenue(): number {
    return this.orders
      .filter(order => order.status !== 'CANCELLED')
      .reduce((total, order) => total + order.orderAmount, 0);
  }

  formatPrice(price: number): string {
    return this.productService.formatPrice(price);
  }

  formatOrderAmount(amount: number): string {
    return this.orderService.formatOrderAmount(amount);
  }

  getOrderStatusColor(status: string): string {
    return this.orderService.getOrderStatusColor(status);
  }

  getOrderStatusText(status: string): string {
    return this.orderService.getOrderStatusText(status);
  }
}
