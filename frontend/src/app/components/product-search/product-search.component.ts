import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';
import { MatCardModule } from '@angular/material/card';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatTableModule } from '@angular/material/table';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { MatDialogModule, MatDialog } from '@angular/material/dialog';
import { ProductService } from '../../services/product.service';
import { OrderService } from '../../services/order.service';
import { AuthService } from '../../services/auth.service';
import { Product } from '../../models/product.model';
import { CreateOrderRequest } from '../../models/order.model';

@Component({
  selector: 'app-product-search',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatCardModule,
    MatFormFieldModule,
    MatInputModule,
    MatButtonModule,
    MatIconModule,
    MatTableModule,
    MatSnackBarModule,
    MatDialogModule
  ],
  template: `
    <div class="product-search-container">
      <mat-card class="search-card">
        <mat-card-header>
          <mat-card-title>
            <mat-icon>search</mat-icon>
            Product Search
          </mat-card-title>
          <mat-card-subtitle>Search for products by name</mat-card-subtitle>
        </mat-card-header>
        
        <mat-card-content>
          <form [formGroup]="searchForm" (ngSubmit)="onSearch()" class="search-form">
            <mat-form-field appearance="outline" class="full-width">
              <mat-label>Product Name</mat-label>
              <input matInput 
                     formControlName="productName" 
                     placeholder="Enter product name to search"
                     (keyup.enter)="onSearch()">
              <mat-icon matSuffix>search</mat-icon>
              <mat-error *ngIf="searchForm.get('productName')?.hasError('required')">
                Product name is required
              </mat-error>
            </mat-form-field>
            
            <div class="search-actions">
              <button mat-raised-button 
                      color="primary" 
                      type="submit" 
                      [disabled]="searchForm.invalid || isSearching">
                <mat-icon *ngIf="isSearching">hourglass_empty</mat-icon>
                <mat-icon *ngIf="!isSearching">search</mat-icon>
                {{ isSearching ? 'Searching...' : 'Search Products' }}
              </button>
              
              <button mat-button 
                      type="button" 
                      (click)="loadAllProducts()"
                      [disabled]="isSearching">
                <mat-icon>list</mat-icon>
                Show All Products
              </button>
              
              <button mat-button 
                      type="button" 
                      (click)="clearSearch()"
                      [disabled]="isSearching">
                <mat-icon>clear</mat-icon>
                Clear
              </button>
            </div>
          </form>
        </mat-card-content>
      </mat-card>

      <!-- Search Results -->
      <mat-card *ngIf="searchResults.length > 0" class="results-card">
        <mat-card-header>
          <mat-card-title>
            <mat-icon>inventory</mat-icon>
            Search Results ({{ searchResults.length }} found)
          </mat-card-title>
        </mat-card-header>
        
        <mat-card-content>
          <div class="table-container">
            <table mat-table [dataSource]="searchResults" class="products-table">
              <!-- Product ID Column -->
              <ng-container matColumnDef="productId">
                <th mat-header-cell *matHeaderCellDef>ID</th>
                <td mat-cell *matCellDef="let product">{{ product.productId }}</td>
              </ng-container>

              <!-- Product Name Column -->
              <ng-container matColumnDef="productName">
                <th mat-header-cell *matHeaderCellDef>Product Name</th>
                <td mat-cell *matCellDef="let product">{{ product.productName }}</td>
              </ng-container>

              <!-- Price Column -->
              <ng-container matColumnDef="price">
                <th mat-header-cell *matHeaderCellDef>Price</th>
                <td mat-cell *matCellDef="let product">{{ formatPrice(product.price) }}</td>
              </ng-container>

              <!-- Quantity Column -->
              <ng-container matColumnDef="quantity">
                <th mat-header-cell *matHeaderCellDef>Available</th>
                <td mat-cell *matCellDef="let product">
                  <span [class]="getQuantityClass(product)">
                    {{ getAvailableQuantity(product) }}
                  </span>
                </td>
              </ng-container>

              <!-- Status Column -->
              <ng-container matColumnDef="status">
                <th mat-header-cell *matHeaderCellDef>Status</th>
                <td mat-cell *matCellDef="let product">
                  <span [class]="getStatusClass(product)">
                    {{ getStatusText(product) }}
                  </span>
                </td>
              </ng-container>

              <!-- Actions Column (Customer only) -->
              <ng-container matColumnDef="actions" *ngIf="authService.isCustomer()">
                <th mat-header-cell *matHeaderCellDef>Actions</th>
                <td mat-cell *matCellDef="let product">
                  <button mat-raised-button 
                          color="primary" 
                          size="small"
                          [disabled]="!isProductAvailable(product) || isOrdering"
                          (click)="addToCart(product)">
                    <mat-icon>add_shopping_cart</mat-icon>
                    Add to Cart
                  </button>
                </td>
              </ng-container>

              <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
              <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
            </table>
          </div>
        </mat-card-content>
      </mat-card>

      <!-- No Results Message -->
      <mat-card *ngIf="hasSearched && searchResults.length === 0" class="no-results-card">
        <mat-card-content>
          <div class="no-results">
            <mat-icon>search_off</mat-icon>
            <h3>No Products Found</h3>
            <p>No products match your search criteria. Try searching with different keywords.</p>
          </div>
        </mat-card-content>
      </mat-card>
    </div>
  `,
  styles: [`
    .product-search-container {
      padding: 24px;
      max-width: 1200px;
      margin: 0 auto;
    }
    
    .search-card, .results-card, .no-results-card {
      margin-bottom: 24px;
    }
    
    .search-form {
      display: flex;
      flex-direction: column;
      gap: 16px;
    }
    
    .full-width {
      width: 100%;
    }
    
    .search-actions {
      display: flex;
      gap: 16px;
      flex-wrap: wrap;
    }
    
    .table-container {
      overflow-x: auto;
      margin-top: 16px;
    }
    
    .products-table {
      width: 100%;
      min-width: 600px;
    }
    
    .products-table th {
      font-weight: 600;
      color: #333;
    }
    
    .quantity-high {
      color: #4caf50;
      font-weight: 500;
    }
    
    .quantity-medium {
      color: #ff9800;
      font-weight: 500;
    }
    
    .quantity-low {
      color: #f44336;
      font-weight: 500;
    }
    
    .status-available {
      color: #4caf50;
      font-weight: 500;
    }
    
    .status-unavailable {
      color: #f44336;
      font-weight: 500;
    }
    
    .no-results {
      text-align: center;
      padding: 48px 24px;
      color: #666;
    }
    
    .no-results mat-icon {
      font-size: 64px;
      width: 64px;
      height: 64px;
      margin-bottom: 16px;
      color: #ccc;
    }
    
    .no-results h3 {
      margin: 16px 0 8px 0;
      color: #333;
    }
    
    .no-results p {
      margin: 0;
    }
    
    @media (max-width: 768px) {
      .product-search-container {
        padding: 16px;
      }
      
      .search-actions {
        flex-direction: column;
      }
      
      .search-actions button {
        width: 100%;
      }
    }
  `]
})
export class ProductSearchComponent implements OnInit {
  searchForm: FormGroup;
  searchResults: Product[] = [];
  isSearching = false;
  isOrdering = false;
  hasSearched = false;
  displayedColumns: string[] = [];

  constructor(
    private formBuilder: FormBuilder,
    private productService: ProductService,
    private orderService: OrderService,
    public authService: AuthService,
    private snackBar: MatSnackBar,
    private dialog: MatDialog
  ) {
    this.searchForm = this.formBuilder.group({
      productName: ['', [Validators.required]]
    });
  }

  ngOnInit() {
    this.setupDisplayColumns();
    this.loadAllProducts(); // Load all products initially
  }

  private setupDisplayColumns() {
    this.displayedColumns = ['productId', 'productName', 'price', 'quantity', 'status'];
    
    if (this.authService.isCustomer()) {
      this.displayedColumns.push('actions');
    }
  }

  onSearch() {
    if (this.searchForm.valid) {
      this.isSearching = true;
      this.hasSearched = true;
      
      const productName = this.searchForm.value.productName;

      this.productService.searchProductsByName(productName).subscribe({
        next: (response) => {
          this.isSearching = false;
          this.searchResults = response.products || [];
          
          if (this.searchResults.length > 0) {
            this.snackBar.open(`Found ${this.searchResults.length} product(s)`, 'Close', {
              duration: 3000,
              panelClass: ['success-snackbar']
            });
          }
        },
        error: (error) => {
          this.isSearching = false;
          this.searchResults = [];
          const errorMessage = error.error?.error || 'Search failed. Please try again.';
          this.snackBar.open(errorMessage, 'Close', {
            duration: 5000,
            panelClass: ['error-snackbar']
          });
        }
      });
    }
  }

  loadAllProducts() {
    this.isSearching = true;
    this.hasSearched = true;

    this.productService.getAllProducts().subscribe({
      next: (response) => {
        this.isSearching = false;
        this.searchResults = response.products || [];
        
        this.snackBar.open(`Loaded ${this.searchResults.length} product(s)`, 'Close', {
          duration: 3000,
          panelClass: ['success-snackbar']
        });
      },
      error: (error) => {
        this.isSearching = false;
        this.searchResults = [];
        const errorMessage = error.error?.error || 'Failed to load products. Please try again.';
        this.snackBar.open(errorMessage, 'Close', {
          duration: 5000,
          panelClass: ['error-snackbar']
        });
      }
    });
  }

  clearSearch() {
    this.searchForm.reset();
    this.searchResults = [];
    this.hasSearched = false;
  }

  addToCart(product: Product) {
    if (!this.isProductAvailable(product)) {
      this.snackBar.open('Product is not available', 'Close', {
        duration: 3000,
        panelClass: ['error-snackbar']
      });
      return;
    }

    this.isOrdering = true;
    const customerId = this.authService.getUserId();
    
    if (!customerId) {
      this.isOrdering = false;
      this.snackBar.open('Please login to place orders', 'Close', {
        duration: 3000,
        panelClass: ['error-snackbar']
      });
      return;
    }

    const orderRequest: CreateOrderRequest = {
      customerId: customerId,
      productId: product.productId!,
      quantity: 1 // Default quantity
    };

    this.orderService.createOrder(orderRequest).subscribe({
      next: (response) => {
        this.isOrdering = false;
        this.snackBar.open('Product added to cart successfully!', 'Close', {
          duration: 3000,
          panelClass: ['success-snackbar']
        });
        
        // Refresh the product list to show updated quantities
        this.loadAllProducts();
      },
      error: (error) => {
        this.isOrdering = false;
        const errorMessage = error.error?.error || 'Failed to add product to cart. Please try again.';
        this.snackBar.open(errorMessage, 'Close', {
          duration: 5000,
          panelClass: ['error-snackbar']
        });
      }
    });
  }

  formatPrice(price: number): string {
    return this.productService.formatPrice(price);
  }

  isProductAvailable(product: Product): boolean {
    return this.productService.isProductAvailable(product);
  }

  getAvailableQuantity(product: Product): number {
    return this.productService.getAvailableQuantity(product);
  }

  getQuantityClass(product: Product): string {
    const available = this.getAvailableQuantity(product);
    if (available > 10) return 'quantity-high';
    if (available > 0) return 'quantity-medium';
    return 'quantity-low';
  }

  getStatusText(product: Product): string {
    return this.isProductAvailable(product) ? 'Available' : 'Out of Stock';
  }

  getStatusClass(product: Product): string {
    return this.isProductAvailable(product) ? 'status-available' : 'status-unavailable';
  }
}
