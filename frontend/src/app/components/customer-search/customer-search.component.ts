import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';
import { MatCardModule } from '@angular/material/card';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatTableModule } from '@angular/material/table';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { CustomerService } from '../../services/customer.service';
import { Customer } from '../../models/customer.model';

@Component({
  selector: 'app-customer-search',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatCardModule,
    MatFormFieldModule,
    MatInputModule,
    MatButtonModule,
    MatIconModule,
    MatTableModule,
    MatSnackBarModule
  ],
  template: `
    <div class="customer-search-container">
      <mat-card class="search-card">
        <mat-card-header>
          <mat-card-title>
            <mat-icon>search</mat-icon>
            Customer Search (Admin Only)
          </mat-card-title>
          <mat-card-subtitle>Search for customers by name</mat-card-subtitle>
        </mat-card-header>
        
        <mat-card-content>
          <form [formGroup]="searchForm" (ngSubmit)="onSearch()" class="search-form">
            <mat-form-field appearance="outline" class="full-width">
              <mat-label>Customer Name</mat-label>
              <input matInput formControlName="customerName" placeholder="Enter customer name to search">
              <mat-icon matSuffix>search</mat-icon>
            </mat-form-field>
            
            <div class="search-actions">
              <button mat-raised-button color="primary" type="submit" [disabled]="searchForm.invalid || isSearching">
                <mat-icon *ngIf="isSearching">hourglass_empty</mat-icon>
                <mat-icon *ngIf="!isSearching">search</mat-icon>
                {{ isSearching ? 'Searching...' : 'Search Customers' }}
              </button>
              
              <button mat-button type="button" (click)="loadAllCustomers()" [disabled]="isSearching">
                <mat-icon>list</mat-icon>
                Show All Customers
              </button>
              
              <button mat-button type="button" (click)="clearSearch()" [disabled]="isSearching">
                <mat-icon>clear</mat-icon>
                Clear
              </button>
            </div>
          </form>
        </mat-card-content>
      </mat-card>

      <!-- Search Results -->
      <mat-card *ngIf="searchResults.length > 0" class="results-card">
        <mat-card-header>
          <mat-card-title>
            <mat-icon>people</mat-icon>
            Search Results ({{ searchResults.length }} found)
          </mat-card-title>
        </mat-card-header>
        
        <mat-card-content>
          <div class="table-container">
            <table mat-table [dataSource]="searchResults" class="customers-table">
              <ng-container matColumnDef="customerId">
                <th mat-header-cell *matHeaderCellDef>ID</th>
                <td mat-cell *matCellDef="let customer">{{ customer.customerId }}</td>
              </ng-container>

              <ng-container matColumnDef="fullName">
                <th mat-header-cell *matHeaderCellDef>Full Name</th>
                <td mat-cell *matCellDef="let customer">{{ customer.fullName }}</td>
              </ng-container>

              <ng-container matColumnDef="email">
                <th mat-header-cell *matHeaderCellDef>Email</th>
                <td mat-cell *matCellDef="let customer">{{ customer.email }}</td>
              </ng-container>

              <ng-container matColumnDef="password">
                <th mat-header-cell *matHeaderCellDef>Password</th>
                <td mat-cell *matCellDef="let customer">{{ customer.password }}</td>
              </ng-container>

              <ng-container matColumnDef="address">
                <th mat-header-cell *matHeaderCellDef>Address</th>
                <td mat-cell *matCellDef="let customer">{{ customer.address }}</td>
              </ng-container>

              <ng-container matColumnDef="contactNumber">
                <th mat-header-cell *matHeaderCellDef>Contact</th>
                <td mat-cell *matCellDef="let customer">{{ customer.contactNumber }}</td>
              </ng-container>

              <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
              <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
            </table>
          </div>
        </mat-card-content>
      </mat-card>

      <!-- No Results Message -->
      <mat-card *ngIf="hasSearched && searchResults.length === 0" class="no-results-card">
        <mat-card-content>
          <div class="no-results">
            <mat-icon>search_off</mat-icon>
            <h3>Customer not found</h3>
            <p>No customers match your search criteria. Try searching with different keywords.</p>
          </div>
        </mat-card-content>
      </mat-card>
    </div>
  `,
  styles: [`
    .customer-search-container {
      padding: 24px;
      max-width: 1200px;
      margin: 0 auto;
    }
    
    .search-card, .results-card, .no-results-card {
      margin-bottom: 24px;
    }
    
    .search-form {
      display: flex;
      flex-direction: column;
      gap: 16px;
    }
    
    .full-width {
      width: 100%;
    }
    
    .search-actions {
      display: flex;
      gap: 16px;
      flex-wrap: wrap;
    }
    
    .table-container {
      overflow-x: auto;
      margin-top: 16px;
    }
    
    .customers-table {
      width: 100%;
      min-width: 800px;
    }
    
    .customers-table th {
      font-weight: 600;
      color: #333;
    }
    
    .no-results {
      text-align: center;
      padding: 48px 24px;
      color: #666;
    }
    
    .no-results mat-icon {
      font-size: 64px;
      width: 64px;
      height: 64px;
      margin-bottom: 16px;
      color: #ccc;
    }
    
    .no-results h3 {
      margin: 16px 0 8px 0;
      color: #333;
    }
    
    .no-results p {
      margin: 0;
    }
    
    @media (max-width: 768px) {
      .customer-search-container {
        padding: 16px;
      }
      
      .search-actions {
        flex-direction: column;
      }
      
      .search-actions button {
        width: 100%;
      }
    }
  `]
})
export class CustomerSearchComponent implements OnInit {
  searchForm: FormGroup;
  searchResults: Customer[] = [];
  isSearching = false;
  hasSearched = false;
  displayedColumns: string[] = ['customerId', 'fullName', 'email', 'password', 'address', 'contactNumber'];

  constructor(
    private formBuilder: FormBuilder,
    private customerService: CustomerService,
    private snackBar: MatSnackBar
  ) {
    this.searchForm = this.formBuilder.group({
      customerName: ['', [Validators.required]]
    });
  }

  ngOnInit() {
    // Component initialization
  }

  onSearch() {
    if (this.searchForm.valid) {
      this.isSearching = true;
      this.hasSearched = true;
      
      const customerName = this.searchForm.value.customerName;

      this.customerService.searchCustomersByName(customerName).subscribe({
        next: (response) => {
          this.isSearching = false;
          this.searchResults = response.customers || [];
          
          if (this.searchResults.length > 0) {
            this.snackBar.open(`Found ${this.searchResults.length} customer(s)`, 'Close', {
              duration: 3000,
              panelClass: ['success-snackbar']
            });
          }
        },
        error: (error) => {
          this.isSearching = false;
          this.searchResults = [];
          const errorMessage = error.error?.error || 'Search failed. Please try again.';
          this.snackBar.open(errorMessage, 'Close', {
            duration: 5000,
            panelClass: ['error-snackbar']
          });
        }
      });
    }
  }

  loadAllCustomers() {
    this.isSearching = true;
    this.hasSearched = true;

    this.customerService.getAllCustomers().subscribe({
      next: (response) => {
        this.isSearching = false;
        this.searchResults = response.customers || [];
        
        this.snackBar.open(`Loaded ${this.searchResults.length} customer(s)`, 'Close', {
          duration: 3000,
          panelClass: ['success-snackbar']
        });
      },
      error: (error) => {
        this.isSearching = false;
        this.searchResults = [];
        const errorMessage = error.error?.error || 'Failed to load customers. Please try again.';
        this.snackBar.open(errorMessage, 'Close', {
          duration: 5000,
          panelClass: ['error-snackbar']
        });
      }
    });
  }

  clearSearch() {
    this.searchForm.reset();
    this.searchResults = [];
    this.hasSearched = false;
  }
}
