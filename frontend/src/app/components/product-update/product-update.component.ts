import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';
import { MatCardModule } from '@angular/material/card';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatTableModule } from '@angular/material/table';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { ProductService } from '../../services/product.service';
import { Product, ProductUpdateRequest } from '../../models/product.model';

@Component({
  selector: 'app-product-update',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatCardModule,
    MatFormFieldModule,
    MatInputModule,
    MatButtonModule,
    MatIconModule,
    MatTableModule,
    MatSnackBarModule
  ],
  template: `
    <div class="product-update-container">
      <mat-card class="search-card">
        <mat-card-header>
          <mat-card-title>
            <mat-icon>edit</mat-icon>
            Product Update (Admin Only)
          </mat-card-title>
          <mat-card-subtitle>Search and update existing products</mat-card-subtitle>
        </mat-card-header>
        
        <mat-card-content>
          <form [formGroup]="searchForm" (ngSubmit)="onSearch()" class="search-form">
            <mat-form-field appearance="outline" class="full-width">
              <mat-label>Product Name or ID</mat-label>
              <input matInput formControlName="searchTerm" placeholder="Enter product name or ID to search">
              <mat-icon matSuffix>search</mat-icon>
            </mat-form-field>
            
            <div class="search-actions">
              <button mat-raised-button color="primary" type="submit" [disabled]="isSearching">
                <mat-icon *ngIf="isSearching">hourglass_empty</mat-icon>
                <mat-icon *ngIf="!isSearching">search</mat-icon>
                {{ isSearching ? 'Searching...' : 'Search Products' }}
              </button>
              
              <button mat-button type="button" (click)="loadAllProducts()" [disabled]="isSearching">
                <mat-icon>list</mat-icon>
                Show All Products
              </button>
            </div>
          </form>
        </mat-card-content>
      </mat-card>

      <!-- Products List -->
      <mat-card *ngIf="products.length > 0" class="products-card">
        <mat-card-header>
          <mat-card-title>
            <mat-icon>inventory</mat-icon>
            Products ({{ products.length }} found)
          </mat-card-title>
        </mat-card-header>
        
        <mat-card-content>
          <div class="table-container">
            <table mat-table [dataSource]="products" class="products-table">
              <ng-container matColumnDef="productId">
                <th mat-header-cell *matHeaderCellDef>ID</th>
                <td mat-cell *matCellDef="let product">{{ product.productId }}</td>
              </ng-container>

              <ng-container matColumnDef="productName">
                <th mat-header-cell *matHeaderCellDef>Name</th>
                <td mat-cell *matCellDef="let product">{{ product.productName }}</td>
              </ng-container>

              <ng-container matColumnDef="price">
                <th mat-header-cell *matHeaderCellDef>Price</th>
                <td mat-cell *matCellDef="let product">{{ formatPrice(product.price) }}</td>
              </ng-container>

              <ng-container matColumnDef="quantity">
                <th mat-header-cell *matHeaderCellDef>Quantity</th>
                <td mat-cell *matCellDef="let product">{{ product.quantity }}</td>
              </ng-container>

              <ng-container matColumnDef="actions">
                <th mat-header-cell *matHeaderCellDef>Actions</th>
                <td mat-cell *matCellDef="let product">
                  <button mat-raised-button color="primary" size="small" (click)="selectProductForUpdate(product)">
                    <mat-icon>edit</mat-icon>
                    Update
                  </button>
                  <button mat-raised-button color="warn" size="small" (click)="deleteProduct(product)" [disabled]="isDeleting">
                    <mat-icon>delete</mat-icon>
                    Delete
                  </button>
                </td>
              </ng-container>

              <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
              <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
            </table>
          </div>
        </mat-card-content>
      </mat-card>

      <!-- Update Form -->
      <mat-card *ngIf="selectedProduct" class="update-card">
        <mat-card-header>
          <mat-card-title>
            <mat-icon>edit</mat-icon>
            Update Product: {{ selectedProduct.productName }}
          </mat-card-title>
        </mat-card-header>
        
        <mat-card-content>
          <form [formGroup]="updateForm" (ngSubmit)="onUpdate()" class="update-form">
            <mat-form-field appearance="outline" class="full-width">
              <mat-label>Product Name</mat-label>
              <input matInput formControlName="productName" placeholder="Enter product name">
              <mat-icon matSuffix>inventory</mat-icon>
            </mat-form-field>
            
            <mat-form-field appearance="outline" class="full-width">
              <mat-label>Price</mat-label>
              <input matInput type="number" step="0.01" min="0" formControlName="price" placeholder="Enter price">
              <span matPrefix>$&nbsp;</span>
              <mat-icon matSuffix>attach_money</mat-icon>
            </mat-form-field>
            
            <mat-form-field appearance="outline" class="full-width">
              <mat-label>Quantity</mat-label>
              <input matInput type="number" min="0" formControlName="quantity" placeholder="Enter quantity">
              <mat-icon matSuffix>inventory_2</mat-icon>
            </mat-form-field>
            
            <mat-form-field appearance="outline" class="full-width">
              <mat-label>Reserved</mat-label>
              <input matInput type="number" min="0" formControlName="reserved" placeholder="Enter reserved quantity">
              <mat-icon matSuffix>lock</mat-icon>
            </mat-form-field>
            
            <div class="form-actions">
              <button mat-raised-button color="primary" type="submit" [disabled]="updateForm.invalid || isUpdating">
                <mat-icon *ngIf="isUpdating">hourglass_empty</mat-icon>
                <mat-icon *ngIf="!isUpdating">save</mat-icon>
                {{ isUpdating ? 'Updating...' : 'Update Product' }}
              </button>
              
              <button mat-button type="button" (click)="cancelUpdate()">
                <mat-icon>cancel</mat-icon>
                Cancel
              </button>
            </div>
          </form>
        </mat-card-content>
      </mat-card>
    </div>
  `,
  styles: [`
    .product-update-container {
      padding: 24px;
      max-width: 1200px;
      margin: 0 auto;
    }
    
    .search-card, .products-card, .update-card {
      margin-bottom: 24px;
    }
    
    .search-form, .update-form {
      display: flex;
      flex-direction: column;
      gap: 16px;
    }
    
    .full-width {
      width: 100%;
    }
    
    .search-actions, .form-actions {
      display: flex;
      gap: 16px;
      flex-wrap: wrap;
    }
    
    .table-container {
      overflow-x: auto;
      margin-top: 16px;
    }
    
    .products-table {
      width: 100%;
      min-width: 700px;
    }
    
    .products-table th {
      font-weight: 600;
      color: #333;
    }
    
    .products-table td button {
      margin-right: 8px;
    }
    
    @media (max-width: 768px) {
      .product-update-container {
        padding: 16px;
      }
      
      .search-actions, .form-actions {
        flex-direction: column;
      }
      
      .search-actions button, .form-actions button {
        width: 100%;
      }
    }
  `]
})
export class ProductUpdateComponent implements OnInit {
  searchForm: FormGroup;
  updateForm: FormGroup;
  products: Product[] = [];
  selectedProduct: Product | null = null;
  isSearching = false;
  isUpdating = false;
  isDeleting = false;
  displayedColumns: string[] = ['productId', 'productName', 'price', 'quantity', 'actions'];

  constructor(
    private formBuilder: FormBuilder,
    private productService: ProductService,
    private snackBar: MatSnackBar
  ) {
    this.searchForm = this.formBuilder.group({
      searchTerm: ['']
    });

    this.updateForm = this.formBuilder.group({
      productName: ['', [Validators.required, Validators.minLength(2)]],
      price: ['', [Validators.required, Validators.min(0.01)]],
      quantity: ['', [Validators.required, Validators.min(0)]],
      reserved: [0, [Validators.min(0)]]
    });
  }

  ngOnInit() {
    this.loadAllProducts();
  }

  onSearch() {
    const searchTerm = this.searchForm.value.searchTerm;
    if (!searchTerm) {
      this.loadAllProducts();
      return;
    }

    this.isSearching = true;
    this.productService.searchProductsByName(searchTerm).subscribe({
      next: (response) => {
        this.isSearching = false;
        this.products = response.products || [];
      },
      error: (error) => {
        this.isSearching = false;
        this.products = [];
        this.snackBar.open('Search failed. Please try again.', 'Close', {
          duration: 5000,
          panelClass: ['error-snackbar']
        });
      }
    });
  }

  loadAllProducts() {
    this.isSearching = true;
    this.productService.getAllProducts().subscribe({
      next: (response) => {
        this.isSearching = false;
        this.products = response.products || [];
      },
      error: (error) => {
        this.isSearching = false;
        this.products = [];
        this.snackBar.open('Failed to load products. Please try again.', 'Close', {
          duration: 5000,
          panelClass: ['error-snackbar']
        });
      }
    });
  }

  selectProductForUpdate(product: Product) {
    this.selectedProduct = product;
    this.updateForm.patchValue({
      productName: product.productName,
      price: product.price,
      quantity: product.quantity,
      reserved: product.reserved || 0
    });
  }

  onUpdate() {
    if (this.updateForm.valid && this.selectedProduct) {
      this.isUpdating = true;
      
      const updateRequest: ProductUpdateRequest = {
        productName: this.updateForm.value.productName,
        price: parseFloat(this.updateForm.value.price),
        quantity: parseInt(this.updateForm.value.quantity),
        reserved: parseInt(this.updateForm.value.reserved) || 0
      };

      this.productService.updateProduct(this.selectedProduct.productId!, updateRequest).subscribe({
        next: (response) => {
          this.isUpdating = false;
          this.snackBar.open('Product updated successfully!', 'Close', {
            duration: 3000,
            panelClass: ['success-snackbar']
          });
          
          this.cancelUpdate();
          this.loadAllProducts();
        },
        error: (error) => {
          this.isUpdating = false;
          const errorMessage = error.error?.error || 'Update failed. Please try again.';
          this.snackBar.open(errorMessage, 'Close', {
            duration: 5000,
            panelClass: ['error-snackbar']
          });
        }
      });
    }
  }

  deleteProduct(product: Product) {
    if (confirm(`Are you sure you want to delete "${product.productName}"?`)) {
      this.isDeleting = true;
      
      this.productService.deleteProduct(product.productId!).subscribe({
        next: (response) => {
          this.isDeleting = false;
          this.snackBar.open('Product deleted successfully!', 'Close', {
            duration: 3000,
            panelClass: ['success-snackbar']
          });
          
          this.loadAllProducts();
          if (this.selectedProduct?.productId === product.productId) {
            this.cancelUpdate();
          }
        },
        error: (error) => {
          this.isDeleting = false;
          const errorMessage = error.error?.error || 'Delete failed. Please try again.';
          this.snackBar.open(errorMessage, 'Close', {
            duration: 5000,
            panelClass: ['error-snackbar']
          });
        }
      });
    }
  }

  cancelUpdate() {
    this.selectedProduct = null;
    this.updateForm.reset();
  }

  formatPrice(price: number): string {
    return this.productService.formatPrice(price);
  }
}
