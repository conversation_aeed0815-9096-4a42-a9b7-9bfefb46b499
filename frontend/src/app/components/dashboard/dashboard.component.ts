import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router } from '@angular/router';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatGridListModule } from '@angular/material/grid-list';
import { AuthService } from '../../services/auth.service';
import { AuthUser } from '../../models/auth.model';

@Component({
  selector: 'app-dashboard',
  standalone: true,
  imports: [
    CommonModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatGridListModule
  ],
  template: `
    <div class="dashboard-container">
      <div class="welcome-section">
        <h1>Welcome to Online Grocery System</h1>
        <p *ngIf="currentUser">
          Hello, <strong>{{ currentUser.fullName || currentUser.username }}</strong>! 
          You are logged in as <strong>{{ currentUser.role }}</strong>.
        </p>
      </div>

      <!-- Admin Dashboard -->
      <div *ngIf="authService.isAdmin()" class="admin-dashboard">
        <h2>Administrator Dashboard</h2>
        <p>Select an option to perform administrative tasks:</p>
        
        <div class="menu-grid">
          <mat-card class="menu-card" (click)="navigate('/customer-search')">
            <mat-card-header>
              <mat-icon mat-card-avatar>search</mat-icon>
              <mat-card-title>Customer Search</mat-card-title>
              <mat-card-subtitle>Search customers by name</mat-card-subtitle>
            </mat-card-header>
            <mat-card-content>
              <p>Find and view customer details by searching their name.</p>
            </mat-card-content>
            <mat-card-actions>
              <button mat-button color="primary">
                <mat-icon>arrow_forward</mat-icon>
                Go to Customer Search
              </button>
            </mat-card-actions>
          </mat-card>

          <mat-card class="menu-card" (click)="navigate('/product-search')">
            <mat-card-header>
              <mat-icon mat-card-avatar>inventory</mat-icon>
              <mat-card-title>Product Search</mat-card-title>
              <mat-card-subtitle>Search products by name</mat-card-subtitle>
            </mat-card-header>
            <mat-card-content>
              <p>Find and view product details by searching their name.</p>
            </mat-card-content>
            <mat-card-actions>
              <button mat-button color="primary">
                <mat-icon>arrow_forward</mat-icon>
                Go to Product Search
              </button>
            </mat-card-actions>
          </mat-card>

          <mat-card class="menu-card" (click)="navigate('/product-registration')">
            <mat-card-header>
              <mat-icon mat-card-avatar>add_box</mat-icon>
              <mat-card-title>Register Product</mat-card-title>
              <mat-card-subtitle>Add new products to inventory</mat-card-subtitle>
            </mat-card-header>
            <mat-card-content>
              <p>Add new products to the system inventory.</p>
            </mat-card-content>
            <mat-card-actions>
              <button mat-button color="primary">
                <mat-icon>arrow_forward</mat-icon>
                Go to Product Registration
              </button>
            </mat-card-actions>
          </mat-card>

          <mat-card class="menu-card" (click)="navigate('/product-update')">
            <mat-card-header>
              <mat-icon mat-card-avatar>edit</mat-icon>
              <mat-card-title>Update Product</mat-card-title>
              <mat-card-subtitle>Modify existing products</mat-card-subtitle>
            </mat-card-header>
            <mat-card-content>
              <p>Update product details, prices, and quantities.</p>
            </mat-card-content>
            <mat-card-actions>
              <button mat-button color="primary">
                <mat-icon>arrow_forward</mat-icon>
                Go to Product Update
              </button>
            </mat-card-actions>
          </mat-card>

          <mat-card class="menu-card" (click)="navigate('/admin-panel')">
            <mat-card-header>
              <mat-icon mat-card-avatar>admin_panel_settings</mat-icon>
              <mat-card-title>Admin Panel</mat-card-title>
              <mat-card-subtitle>Complete administrative control</mat-card-subtitle>
            </mat-card-header>
            <mat-card-content>
              <p>Access all administrative functions and system management.</p>
            </mat-card-content>
            <mat-card-actions>
              <button mat-button color="primary">
                <mat-icon>arrow_forward</mat-icon>
                Go to Admin Panel
              </button>
            </mat-card-actions>
          </mat-card>
        </div>
      </div>

      <!-- Customer Dashboard -->
      <div *ngIf="authService.isCustomer()" class="customer-dashboard">
        <h2>Customer Dashboard</h2>
        <p>Welcome to your personal shopping dashboard:</p>
        
        <div class="menu-grid">
          <mat-card class="menu-card" (click)="navigate('/product-search')">
            <mat-card-header>
              <mat-icon mat-card-avatar>search</mat-icon>
              <mat-card-title>Product Search</mat-card-title>
              <mat-card-subtitle>Find products to order</mat-card-subtitle>
            </mat-card-header>
            <mat-card-content>
              <p>Search for products by name and add them to your cart.</p>
            </mat-card-content>
            <mat-card-actions>
              <button mat-button color="primary">
                <mat-icon>arrow_forward</mat-icon>
                Go to Product Search
              </button>
            </mat-card-actions>
          </mat-card>

          <mat-card class="menu-card" (click)="navigate('/order-history')">
            <mat-card-header>
              <mat-icon mat-card-avatar>history</mat-icon>
              <mat-card-title>Order History</mat-card-title>
              <mat-card-subtitle>View your past orders</mat-card-subtitle>
            </mat-card-header>
            <mat-card-content>
              <p>Check your order history and track current orders.</p>
            </mat-card-content>
            <mat-card-actions>
              <button mat-button color="primary">
                <mat-icon>arrow_forward</mat-icon>
                Go to Order History
              </button>
            </mat-card-actions>
          </mat-card>

          <mat-card class="menu-card" (click)="navigate('/customer-update')">
            <mat-card-header>
              <mat-icon mat-card-avatar>person</mat-icon>
              <mat-card-title>Update Profile</mat-card-title>
              <mat-card-subtitle>Manage your account details</mat-card-subtitle>
            </mat-card-header>
            <mat-card-content>
              <p>Update your personal information and account settings.</p>
            </mat-card-content>
            <mat-card-actions>
              <button mat-button color="primary">
                <mat-icon>arrow_forward</mat-icon>
                Go to Profile Update
              </button>
            </mat-card-actions>
          </mat-card>
        </div>
      </div>

      <!-- Quick Actions -->
      <div class="quick-actions">
        <h3>Quick Actions</h3>
        <div class="action-buttons">
          <button mat-raised-button color="accent" (click)="logout()">
            <mat-icon>logout</mat-icon>
            Logout
          </button>
        </div>
      </div>
    </div>
  `,
  styles: [`
    .dashboard-container {
      padding: 24px;
      max-width: 1200px;
      margin: 0 auto;
    }
    
    .welcome-section {
      text-align: center;
      margin-bottom: 32px;
      padding: 24px;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      border-radius: 8px;
    }
    
    .welcome-section h1 {
      margin: 0 0 16px 0;
      font-size: 2rem;
    }
    
    .welcome-section p {
      margin: 0;
      font-size: 1.1rem;
    }
    
    .admin-dashboard, .customer-dashboard {
      margin-bottom: 32px;
    }
    
    .admin-dashboard h2, .customer-dashboard h2 {
      color: #333;
      margin-bottom: 8px;
    }
    
    .admin-dashboard p, .customer-dashboard p {
      color: #666;
      margin-bottom: 24px;
    }
    
    .menu-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
      gap: 24px;
      margin-bottom: 32px;
    }
    
    .menu-card {
      cursor: pointer;
      transition: transform 0.2s ease, box-shadow 0.2s ease;
      height: 100%;
    }
    
    .menu-card:hover {
      transform: translateY(-4px);
      box-shadow: 0 8px 24px rgba(0,0,0,0.15);
    }
    
    .menu-card mat-card-header {
      margin-bottom: 16px;
    }
    
    .menu-card mat-card-content {
      margin-bottom: 16px;
    }
    
    .menu-card mat-card-content p {
      margin: 0;
      color: #666;
    }
    
    .quick-actions {
      text-align: center;
      padding: 24px;
      background-color: #f5f5f5;
      border-radius: 8px;
    }
    
    .quick-actions h3 {
      margin: 0 0 16px 0;
      color: #333;
    }
    
    .action-buttons {
      display: flex;
      justify-content: center;
      gap: 16px;
      flex-wrap: wrap;
    }
    
    @media (max-width: 768px) {
      .dashboard-container {
        padding: 16px;
      }
      
      .menu-grid {
        grid-template-columns: 1fr;
        gap: 16px;
      }
      
      .welcome-section h1 {
        font-size: 1.5rem;
      }
      
      .action-buttons {
        flex-direction: column;
        align-items: center;
      }
    }
  `]
})
export class DashboardComponent implements OnInit {
  currentUser: AuthUser | null = null;

  constructor(
    public authService: AuthService,
    private router: Router
  ) {}

  ngOnInit() {
    this.currentUser = this.authService.getCurrentUser();
  }

  navigate(route: string) {
    this.router.navigate([route]);
  }

  logout() {
    this.authService.logout().subscribe({
      next: () => {
        this.router.navigate(['/login']);
      },
      error: (error) => {
        console.error('Logout error:', error);
        this.router.navigate(['/login']);
      }
    });
  }
}
