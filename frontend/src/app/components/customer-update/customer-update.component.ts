import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';
import { MatCardModule } from '@angular/material/card';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { CustomerService } from '../../services/customer.service';
import { AuthService } from '../../services/auth.service';
import { Customer, CustomerUpdateRequest } from '../../models/customer.model';

@Component({
  selector: 'app-customer-update',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatCardModule,
    MatFormFieldModule,
    MatInputModule,
    MatButtonModule,
    MatIconModule,
    MatSnackBarModule
  ],
  template: `
    <div class="update-container">
      <mat-card class="update-card">
        <mat-card-header>
          <mat-card-title>
            <mat-icon>person</mat-icon>
            Update Customer Details
          </mat-card-title>
          <mat-card-subtitle>Modify your account information</mat-card-subtitle>
        </mat-card-header>
        
        <mat-card-content>
          <form [formGroup]="updateForm" (ngSubmit)="onSubmit()" class="update-form" *ngIf="!isLoading">
            <mat-form-field appearance="outline" class="full-width">
              <mat-label>Full Name</mat-label>
              <input matInput formControlName="fullName" placeholder="Enter your full name">
              <mat-icon matSuffix>person</mat-icon>
            </mat-form-field>
            
            <mat-form-field appearance="outline" class="full-width">
              <mat-label>Email</mat-label>
              <input matInput formControlName="email" placeholder="Enter your email">
              <mat-icon matSuffix>email</mat-icon>
            </mat-form-field>
            
            <mat-form-field appearance="outline" class="full-width">
              <mat-label>New Password (optional)</mat-label>
              <input matInput [type]="hidePassword ? 'password' : 'text'" formControlName="password" placeholder="Leave blank to keep current password">
              <button mat-icon-button matSuffix (click)="hidePassword = !hidePassword" type="button">
                <mat-icon>{{hidePassword ? 'visibility_off' : 'visibility'}}</mat-icon>
              </button>
            </mat-form-field>
            
            <mat-form-field appearance="outline" class="full-width">
              <mat-label>Address</mat-label>
              <textarea matInput formControlName="address" placeholder="Enter your address" rows="3"></textarea>
              <mat-icon matSuffix>home</mat-icon>
            </mat-form-field>
            
            <mat-form-field appearance="outline" class="full-width">
              <mat-label>Contact Number</mat-label>
              <input matInput formControlName="contactNumber" placeholder="Enter 10-digit phone number">
              <mat-icon matSuffix>phone</mat-icon>
            </mat-form-field>
            
            <button mat-raised-button color="primary" type="submit" class="full-width" [disabled]="updateForm.invalid || isUpdating">
              <mat-icon *ngIf="isUpdating">hourglass_empty</mat-icon>
              <mat-icon *ngIf="!isUpdating">save</mat-icon>
              {{ isUpdating ? 'Updating...' : 'Update Details' }}
            </button>
          </form>
          
          <div *ngIf="isLoading" class="loading">
            <mat-icon>hourglass_empty</mat-icon>
            <p>Loading customer details...</p>
          </div>
        </mat-card-content>
      </mat-card>
    </div>
  `,
  styles: [`
    .update-container {
      padding: 24px;
      max-width: 600px;
      margin: 0 auto;
    }
    
    .update-form {
      display: flex;
      flex-direction: column;
      gap: 16px;
      margin-top: 16px;
    }
    
    .full-width {
      width: 100%;
    }
    
    .loading {
      text-align: center;
      padding: 48px;
      color: #666;
    }
    
    .loading mat-icon {
      font-size: 48px;
      width: 48px;
      height: 48px;
      margin-bottom: 16px;
    }
  `]
})
export class CustomerUpdateComponent implements OnInit {
  updateForm: FormGroup;
  hidePassword = true;
  isLoading = true;
  isUpdating = false;
  customerId: number | null = null;

  constructor(
    private formBuilder: FormBuilder,
    private customerService: CustomerService,
    private authService: AuthService,
    private snackBar: MatSnackBar
  ) {
    this.updateForm = this.formBuilder.group({
      fullName: ['', [Validators.required, Validators.minLength(2)]],
      email: ['', [Validators.required, Validators.email]],
      password: [''],
      address: ['', [Validators.required]],
      contactNumber: ['', [Validators.required, Validators.pattern(/^\d{10}$/)]]
    });
  }

  ngOnInit() {
    this.customerId = this.authService.getUserId();
    if (this.customerId) {
      this.loadCustomerDetails();
    }
  }

  loadCustomerDetails() {
    if (!this.customerId) return;
    
    this.customerService.getCustomerById(this.customerId).subscribe({
      next: (response) => {
        const customer = response.customer;
        this.updateForm.patchValue({
          fullName: customer.fullName,
          email: customer.email,
          address: customer.address,
          contactNumber: customer.contactNumber
        });
        this.isLoading = false;
      },
      error: (error) => {
        this.isLoading = false;
        this.snackBar.open('Failed to load customer details', 'Close', {
          duration: 5000,
          panelClass: ['error-snackbar']
        });
      }
    });
  }

  onSubmit() {
    if (this.updateForm.valid && this.customerId) {
      this.isUpdating = true;
      
      const updateRequest: CustomerUpdateRequest = {
        fullName: this.updateForm.value.fullName,
        email: this.updateForm.value.email,
        address: this.updateForm.value.address,
        contactNumber: this.updateForm.value.contactNumber
      };
      
      if (this.updateForm.value.password) {
        updateRequest.password = this.updateForm.value.password;
      }

      this.customerService.updateCustomer(this.customerId, updateRequest).subscribe({
        next: (response) => {
          this.isUpdating = false;
          this.snackBar.open('Customer details updated successfully!', 'Close', {
            duration: 3000,
            panelClass: ['success-snackbar']
          });
        },
        error: (error) => {
          this.isUpdating = false;
          const errorMessage = error.error?.error || 'Update failed. Please try again.';
          this.snackBar.open(errorMessage, 'Close', {
            duration: 5000,
            panelClass: ['error-snackbar']
          });
        }
      });
    }
  }
}
