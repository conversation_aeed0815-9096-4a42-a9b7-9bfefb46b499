import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';
import { Router } from '@angular/router';
import { MatCardModule } from '@angular/material/card';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { CustomerService } from '../../services/customer.service';
import { CustomerRegistrationRequest } from '../../models/customer.model';

@Component({
  selector: 'app-customer-registration',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatCardModule,
    MatFormFieldModule,
    MatInputModule,
    MatButtonModule,
    MatIconModule,
    MatSnackBarModule
  ],
  template: `
    <div class="registration-container">
      <mat-card class="registration-card">
        <mat-card-header>
          <mat-card-title class="registration-title">
            <mat-icon>person_add</mat-icon>
            Customer Registration
          </mat-card-title>
          <mat-card-subtitle>Create your account to start shopping</mat-card-subtitle>
        </mat-card-header>
        
        <mat-card-content>
          <form [formGroup]="registrationForm" (ngSubmit)="onSubmit()" class="registration-form">
            <mat-form-field appearance="outline" class="full-width">
              <mat-label>Full Name</mat-label>
              <input matInput 
                     formControlName="fullName" 
                     placeholder="Enter your full name"
                     autocomplete="name">
              <mat-icon matSuffix>person</mat-icon>
              <mat-error *ngIf="registrationForm.get('fullName')?.hasError('required')">
                Full name is required
              </mat-error>
              <mat-error *ngIf="registrationForm.get('fullName')?.hasError('minlength')">
                Full name must be at least 2 characters long
              </mat-error>
            </mat-form-field>
            
            <mat-form-field appearance="outline" class="full-width">
              <mat-label>Email</mat-label>
              <input matInput 
                     formControlName="email" 
                     placeholder="Enter your email address"
                     autocomplete="email">
              <mat-icon matSuffix>email</mat-icon>
              <mat-error *ngIf="registrationForm.get('email')?.hasError('required')">
                Email is required
              </mat-error>
              <mat-error *ngIf="registrationForm.get('email')?.hasError('email')">
                Please enter a valid email address
              </mat-error>
            </mat-form-field>
            
            <mat-form-field appearance="outline" class="full-width">
              <mat-label>Password</mat-label>
              <input matInput 
                     [type]="hidePassword ? 'password' : 'text'"
                     formControlName="password" 
                     placeholder="Enter password"
                     autocomplete="new-password">
              <button mat-icon-button matSuffix 
                      (click)="hidePassword = !hidePassword" 
                      [attr.aria-label]="'Hide password'" 
                      [attr.aria-pressed]="hidePassword"
                      type="button">
                <mat-icon>{{hidePassword ? 'visibility_off' : 'visibility'}}</mat-icon>
              </button>
              <mat-error *ngIf="registrationForm.get('password')?.hasError('required')">
                Password is required
              </mat-error>
              <mat-error *ngIf="registrationForm.get('password')?.hasError('pattern')">
                Password must contain at least 8 characters with uppercase, lowercase, digit, and special character
              </mat-error>
            </mat-form-field>
            
            <mat-form-field appearance="outline" class="full-width">
              <mat-label>Address</mat-label>
              <textarea matInput 
                        formControlName="address" 
                        placeholder="Enter your full address"
                        rows="3"
                        autocomplete="street-address"></textarea>
              <mat-icon matSuffix>home</mat-icon>
              <mat-error *ngIf="registrationForm.get('address')?.hasError('required')">
                Address is required
              </mat-error>
            </mat-form-field>
            
            <mat-form-field appearance="outline" class="full-width">
              <mat-label>Contact Number</mat-label>
              <input matInput 
                     formControlName="contactNumber" 
                     placeholder="Enter 10-digit phone number"
                     autocomplete="tel">
              <mat-icon matSuffix>phone</mat-icon>
              <mat-error *ngIf="registrationForm.get('contactNumber')?.hasError('required')">
                Contact number is required
              </mat-error>
              <mat-error *ngIf="registrationForm.get('contactNumber')?.hasError('pattern')">
                Contact number must be exactly 10 digits
              </mat-error>
            </mat-form-field>
            
            <button mat-raised-button 
                    color="primary" 
                    type="submit" 
                    class="register-button full-width"
                    [disabled]="registrationForm.invalid || isLoading">
              <mat-icon *ngIf="isLoading">hourglass_empty</mat-icon>
              <mat-icon *ngIf="!isLoading">person_add</mat-icon>
              {{ isLoading ? 'Registering...' : 'Register' }}
            </button>
          </form>
        </mat-card-content>
        
        <mat-card-actions class="registration-actions">
          <button mat-button 
                  color="accent" 
                  (click)="goToLogin()"
                  class="full-width">
            <mat-icon>login</mat-icon>
            Already have an account? Login Here
          </button>
        </mat-card-actions>
      </mat-card>
    </div>
  `,
  styles: [`
    .registration-container {
      display: flex;
      justify-content: center;
      align-items: center;
      min-height: 100vh;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      padding: 20px;
    }
    
    .registration-card {
      width: 100%;
      max-width: 500px;
      box-shadow: 0 8px 32px rgba(0,0,0,0.1);
    }
    
    .registration-title {
      display: flex;
      align-items: center;
      gap: 8px;
      color: #333;
      font-size: 1.5rem;
    }
    
    .registration-form {
      display: flex;
      flex-direction: column;
      gap: 16px;
      margin-top: 16px;
    }
    
    .full-width {
      width: 100%;
    }
    
    .register-button {
      height: 48px;
      font-size: 16px;
      margin-top: 8px;
    }
    
    .registration-actions {
      padding: 16px;
      display: flex;
      justify-content: center;
    }
    
    @media (max-width: 480px) {
      .registration-container {
        padding: 10px;
      }
      
      .registration-card {
        max-width: 100%;
      }
    }
  `]
})
export class CustomerRegistrationComponent implements OnInit {
  registrationForm: FormGroup;
  hidePassword = true;
  isLoading = false;

  constructor(
    private formBuilder: FormBuilder,
    private customerService: CustomerService,
    private router: Router,
    private snackBar: MatSnackBar
  ) {
    this.registrationForm = this.formBuilder.group({
      fullName: ['', [Validators.required, Validators.minLength(2), Validators.maxLength(100)]],
      email: ['', [Validators.required, Validators.email]],
      password: ['', [
        Validators.required,
        Validators.pattern(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/)
      ]],
      address: ['', [Validators.required]],
      contactNumber: ['', [Validators.required, Validators.pattern(/^\d{10}$/)]]
    });
  }

  ngOnInit() {
    // Component initialization
  }

  onSubmit() {
    if (this.registrationForm.valid) {
      this.isLoading = true;
      
      const registrationRequest: CustomerRegistrationRequest = {
        fullName: this.registrationForm.value.fullName,
        email: this.registrationForm.value.email,
        password: this.registrationForm.value.password,
        address: this.registrationForm.value.address,
        contactNumber: this.registrationForm.value.contactNumber
      };

      // Validate using service
      const validationErrors = this.customerService.validateCustomerData(registrationRequest);
      if (validationErrors.length > 0) {
        this.isLoading = false;
        this.snackBar.open(validationErrors.join(', '), 'Close', {
          duration: 5000,
          panelClass: ['error-snackbar']
        });
        return;
      }

      this.customerService.registerCustomer(registrationRequest).subscribe({
        next: (response) => {
          this.isLoading = false;
          this.snackBar.open(response.message, 'Close', {
            duration: 3000,
            panelClass: ['success-snackbar']
          });
          
          // Navigate to login page
          this.router.navigate(['/login']);
        },
        error: (error) => {
          this.isLoading = false;
          const errorMessage = error.error?.error || 'Registration failed. Please try again.';
          this.snackBar.open(errorMessage, 'Close', {
            duration: 5000,
            panelClass: ['error-snackbar']
          });
        }
      });
    }
  }

  goToLogin() {
    this.router.navigate(['/login']);
  }
}
