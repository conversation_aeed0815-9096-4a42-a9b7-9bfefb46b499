# Online Grocery Ordering System

A complete full-stack web application for online grocery ordering built with Java Spring Boot backend and Angular frontend.

## 🚀 Quick Start

### One-Command Setup
```bash
chmod +x start.sh && ./start.sh
```

This will automatically:
- Check system requirements
- Install all dependencies
- Build both applications
- Start backend and frontend servers
- Open the application at http://localhost:4200

## 📋 Project Overview

This is a comprehensive online grocery ordering system that implements all the specified user stories with modern web technologies, security best practices, and a professional user interface.

### 🎯 Key Features

- **User Authentication & Authorization** - JWT-based secure login system
- **Customer Registration** (US002) - Complete customer onboarding with validation
- **Customer Profile Management** (US003) - Update personal details and preferences
- **Order History** (US004) - View past orders and track current ones
- **Customer Search** (US005) - Admin functionality to search customers by name
- **Product Search** (US006) - Find products by name with real-time results
- **Product Management** (US007-US009) - Admin tools for product CRUD operations
- **SQL Injection Prevention** (US010) - Comprehensive security implementation
- **Responsive Design** - Works on desktop, tablet, and mobile devices

## 🏗️ Architecture

### Backend (Spring Boot)
- **Framework**: Spring Boot 3.2.0 with Java 17
- **Database**: H2 (in-memory) with JDBC connectivity
- **Security**: Spring Security with JWT authentication
- **API**: RESTful APIs with comprehensive error handling
- **Validation**: Input validation and SQL injection prevention

### Frontend (Angular)
- **Framework**: Angular 17 with TypeScript
- **UI Library**: Angular Material Design
- **Styling**: Custom CSS with Bootstrap integration
- **State Management**: RxJS observables and services
- **Routing**: Angular Router with guards

## 📁 Project Structure

```
online-grocery-system/
├── backend/                    # Spring Boot Application
│   ├── src/main/java/com/grocery/
│   │   ├── OnlineGroceryApplication.java
│   │   ├── controller/         # REST Controllers
│   │   ├── service/           # Business Logic
│   │   ├── repository/        # Data Access Layer
│   │   ├── model/             # Entity Classes
│   │   ├── security/          # Security Configuration
│   │   └── config/            # Application Configuration
│   ├── src/main/resources/
│   │   ├── application.properties
│   │   ├── schema.sql         # Database Schema
│   │   └── data.sql           # Sample Data
│   └── pom.xml               # Maven Dependencies
├── frontend/                  # Angular Application
│   ├── src/app/
│   │   ├── components/        # UI Components
│   │   ├── services/          # API Services
│   │   ├── guards/            # Route Guards
│   │   ├── models/            # TypeScript Models
│   │   └── app.component.ts   # Root Component
│   ├── package.json          # NPM Dependencies
│   └── angular.json          # Angular Configuration
├── scripts/                   # Startup Scripts
├── logs/                     # Application Logs
├── start.sh                  # Main Startup Script
└── README.md                 # This File
```

## 🔧 System Requirements

- **Java**: 17 or higher
- **Maven**: 3.6 or higher
- **Node.js**: 18 or higher
- **npm**: 9 or higher
- **Operating System**: Linux, macOS, or Windows with WSL

## 🚀 Installation & Setup

### Method 1: Automatic Setup (Recommended)
```bash
# Clone the repository
git clone <repository-url>
cd online-grocery-system

# Run the automatic setup script
chmod +x start.sh
./start.sh
```

### Method 2: Manual Setup

#### Backend Setup
```bash
cd backend
mvn clean install
mvn spring-boot:run
```

#### Frontend Setup
```bash
cd frontend
npm install
npm install -g @angular/cli@17
ng serve --host 0.0.0.0 --port 4200
```

## 🌐 Application URLs

- **Frontend Application**: http://localhost:4200
- **Backend API**: http://localhost:8080/api
- **H2 Database Console**: http://localhost:8080/api/h2-console
  - JDBC URL: `jdbc:h2:mem:grocerydb`
  - Username: `sa`
  - Password: `password`

## 👤 Demo Credentials

### Administrator
- **Username**: `admin`
- **Password**: `admin123`

### Sample Customer
- **Email**: `<EMAIL>`
- **Password**: `secret`

## 🎮 User Stories Implementation

### US001: Online Grocery Ordering
✅ **Implemented**: Complete menu-driven system with role-based access control

### US002: Customer Registration
✅ **Implemented**:
- Full name validation (2-100 characters)
- Email validation with uniqueness check
- Password complexity requirements (8+ chars, uppercase, lowercase, digit, special char)
- Address validation
- Contact number validation (exactly 10 digits)

### US003: Update Customer Details
✅ **Implemented**:
- Authentication required
- All field validation
- Email uniqueness check on updates
- Optional password updates

### US004: Get Customer Order Details
✅ **Implemented**:
- Customer ID-based order retrieval
- Complete order history with product details
- Order status tracking

### US005: Search Customer by Name (Admin)
✅ **Implemented**:
- Case-insensitive search
- Admin-only access
- Password masking for security
- "Customer not found" error handling

### US006: Search Product by Name
✅ **Implemented**:
- Case-insensitive product search
- Real-time availability checking
- Add to cart functionality
- "Product not found" error handling

### US007: Register Product (Admin)
✅ **Implemented**:
- Product name validation
- Price validation (positive numbers only)
- Quantity validation (non-negative)
- System-generated product IDs

### US008: Update Product (Admin)
✅ **Implemented**:
- All field updates supported
- Validation for all inputs
- Reserved quantity management

### US009: Delete Product (Admin)
✅ **Implemented**:
- Product existence validation
- Confirmation dialogs
- Cascade handling for related orders

### US010: SQL Injection Prevention
✅ **Implemented**:
- PreparedStatements throughout
- Input validation and sanitization
- Parameterized queries
- Security headers and CORS configuration

## 🔒 Security Features

- **JWT Authentication**: Secure token-based authentication
- **Password Encryption**: BCrypt hashing for all passwords
- **SQL Injection Prevention**: PreparedStatements and input validation
- **CORS Configuration**: Proper cross-origin resource sharing setup
- **Input Validation**: Comprehensive server-side and client-side validation
- **Role-Based Access Control**: Admin and Customer role separation

## 🎨 UI/UX Features

- **Responsive Design**: Works on all device sizes
- **Material Design**: Modern, intuitive interface
- **Real-time Feedback**: Loading states and error messages
- **Accessibility**: WCAG compliant design
- **Professional Styling**: Clean, modern appearance

## 📊 Database Schema

### Tables
- **admin**: Administrator accounts
- **customers**: Customer information and credentials
- **products**: Product catalog with inventory
- **orders**: Order history and tracking

### Key Relationships
- Orders → Customers (Many-to-One)
- Orders → Products (Many-to-One)
- Products → Customers (Reserved by)

## 🔧 API Endpoints

### Authentication
- `POST /api/auth/login` - User login
- `POST /api/auth/logout` - User logout
- `POST /api/auth/validate` - Token validation

### Customer Management
- `POST /api/customers/register` - Customer registration
- `PUT /api/customers/update/{id}` - Update customer details
- `GET /api/customers/{id}/orders` - Get customer orders
- `GET /api/customers/search` - Search customers (Admin)

### Product Management
- `GET /api/products/search` - Search products
- `POST /api/products/register` - Register product (Admin)
- `PUT /api/products/update/{id}` - Update product (Admin)
- `DELETE /api/products/delete/{id}` - Delete product (Admin)

### Order Management
- `POST /api/orders/create` - Create new order
- `GET /api/orders/{id}` - Get order details
- `PUT /api/orders/{id}/cancel` - Cancel order

## 🧪 Testing

The application includes comprehensive validation and error handling:

- **Input Validation**: All forms validate data before submission
- **Error Handling**: Graceful error messages for all failure scenarios
- **Security Testing**: SQL injection prevention verified
- **Cross-browser Compatibility**: Tested on modern browsers

## 📝 Logging

Application logs are stored in:
- **Backend**: `logs/backend.log`
- **Frontend**: `logs/frontend.log`

## 🚀 Deployment

### GitHub Codespaces
The application is ready for GitHub Codespaces deployment:

1. Open in Codespaces
2. Run `./start.sh`
3. Access via forwarded ports

### Production Deployment
For production deployment:
1. Update `application.properties` for production database
2. Build frontend: `ng build --prod`
3. Package backend: `mvn clean package`
4. Deploy JAR file and static assets

## 👨‍💻 Author

**Chirag Singhal** ([@chirag127](https://github.com/chirag127))

## 📄 License

This project is licensed under the MIT License.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Commit your changes
4. Push to the branch
5. Create a Pull Request

---

**Last Updated**: 2024-12-19 UTC

For support or questions, please open an issue in the repository.
