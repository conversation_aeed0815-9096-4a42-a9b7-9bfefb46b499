# Server Configuration
server.port=8080
server.servlet.context-path=/api

# Database Configuration (H2 for development)
spring.datasource.url=jdbc:h2:mem:grocerydb
spring.datasource.driverClassName=org.h2.Driver
spring.datasource.username=sa
spring.datasource.password=password
spring.h2.console.enabled=true
spring.h2.console.path=/h2-console

# JPA Configuration
spring.jpa.database-platform=org.hibernate.dialect.H2Dialect
spring.jpa.hibernate.ddl-auto=create-drop
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.format_sql=true

# Initialize database with schema
spring.sql.init.mode=always
spring.sql.init.schema-locations=classpath:schema.sql
spring.sql.init.data-locations=classpath:data.sql

# JWT Configuration
jwt.secret=mySecretKey123456789012345678901234567890
jwt.expiration=86400000

# CORS Configuration
cors.allowed.origins=http://localhost:4200

# Logging
logging.level.com.grocery=DEBUG
logging.level.org.springframework.security=DEBUG
logging.level.org.springframework.web=DEBUG

# Security
spring.security.user.name=admin
spring.security.user.password=admin123
spring.security.user.roles=ADMIN
