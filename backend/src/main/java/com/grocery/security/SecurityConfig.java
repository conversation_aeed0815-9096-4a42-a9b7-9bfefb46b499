package com.grocery.security;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.config.annotation.method.configuration.EnableMethodSecurity;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;
import org.springframework.security.web.header.writers.ReferrerPolicyHeaderWriter;

@Configuration
@EnableWebSecurity
@EnableMethodSecurity(prePostEnabled = true)
public class SecurityConfig {

    @Autowired
    private JwtAuthenticationFilter jwtAuthenticationFilter;

    @Bean
    public SecurityFilterChain filterChain(HttpSecurity http) throws Exception {
        http.csrf(csrf -> csrf.disable())
            .sessionManagement(session -> session.sessionCreationPolicy(SessionCreationPolicy.STATELESS))
            .authorizeHttpRequests(authz -> authz
                // Public endpoints
                .requestMatchers("/api/auth/**").permitAll()
                .requestMatchers("/api/customers/register").permitAll()
                .requestMatchers("/h2-console/**").permitAll()
                .requestMatchers("/api/").permitAll()
                
                // Admin only endpoints
                .requestMatchers("/api/admin/**").hasRole("ADMIN")
                .requestMatchers("/api/customers/search/**").hasRole("ADMIN")
                .requestMatchers("/api/products/register").hasRole("ADMIN")
                .requestMatchers("/api/products/update/**").hasRole("ADMIN")
                .requestMatchers("/api/products/delete/**").hasRole("ADMIN")
                
                // Customer endpoints
                .requestMatchers("/api/customers/update/**").hasAnyRole("CUSTOMER", "ADMIN")
                .requestMatchers("/api/customers/orders/**").hasAnyRole("CUSTOMER", "ADMIN")
                .requestMatchers("/api/products/search/**").hasAnyRole("CUSTOMER", "ADMIN")
                .requestMatchers("/api/orders/**").hasAnyRole("CUSTOMER", "ADMIN")
                
                // All other requests need authentication
                .anyRequest().authenticated()
            )
            .headers(headers -> headers
                .frameOptions().sameOrigin() // Allow H2 console frames
                .referrerPolicy(ReferrerPolicyHeaderWriter.ReferrerPolicy.STRICT_ORIGIN_WHEN_CROSS_ORIGIN)
            )
            .addFilterBefore(jwtAuthenticationFilter, UsernamePasswordAuthenticationFilter.class);

        return http.build();
    }
}
