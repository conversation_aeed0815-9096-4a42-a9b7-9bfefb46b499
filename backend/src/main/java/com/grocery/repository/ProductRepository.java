package com.grocery.repository;

import com.grocery.model.Product;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.EmptyResultDataAccessException;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.jdbc.support.GeneratedKeyHolder;
import org.springframework.jdbc.support.KeyHolder;
import org.springframework.stereotype.Repository;

import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Repository
public class ProductRepository {

    @Autowired
    private JdbcTemplate jdbcTemplate;

    private final RowMapper<Product> productRowMapper = new RowMapper<Product>() {
        @Override
        public Product mapRow(ResultSet rs, int rowNum) throws SQLException {
            Product product = new Product();
            product.setProductId(rs.getLong("product_id"));
            product.setProductName(rs.getString("product_name"));
            product.setPrice(rs.getBigDecimal("price"));
            product.setQuantity(rs.getInt("quantity"));
            product.setReserved(rs.getInt("reserved"));
            
            Long customerId = rs.getLong("customer_id");
            if (!rs.wasNull()) {
                product.setCustomerId(customerId);
            }
            
            Timestamp createdAt = rs.getTimestamp("created_at");
            if (createdAt != null) {
                product.setCreatedAt(createdAt.toLocalDateTime());
            }
            
            Timestamp updatedAt = rs.getTimestamp("updated_at");
            if (updatedAt != null) {
                product.setUpdatedAt(updatedAt.toLocalDateTime());
            }
            
            return product;
        }
    };

    public Product save(Product product) {
        String sql = "INSERT INTO products (product_name, price, quantity, reserved, customer_id, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?)";
        
        KeyHolder keyHolder = new GeneratedKeyHolder();
        LocalDateTime now = LocalDateTime.now();
        
        jdbcTemplate.update(connection -> {
            PreparedStatement ps = connection.prepareStatement(sql, Statement.RETURN_GENERATED_KEYS);
            ps.setString(1, product.getProductName());
            ps.setBigDecimal(2, product.getPrice());
            ps.setInt(3, product.getQuantity());
            ps.setInt(4, product.getReserved() != null ? product.getReserved() : 0);
            if (product.getCustomerId() != null) {
                ps.setLong(5, product.getCustomerId());
            } else {
                ps.setNull(5, java.sql.Types.BIGINT);
            }
            ps.setTimestamp(6, Timestamp.valueOf(now));
            ps.setTimestamp(7, Timestamp.valueOf(now));
            return ps;
        }, keyHolder);
        
        product.setProductId(keyHolder.getKey().longValue());
        product.setCreatedAt(now);
        product.setUpdatedAt(now);
        
        return product;
    }

    public Optional<Product> findById(Long id) {
        String sql = "SELECT * FROM products WHERE product_id = ?";
        try {
            Product product = jdbcTemplate.queryForObject(sql, productRowMapper, id);
            return Optional.of(product);
        } catch (EmptyResultDataAccessException e) {
            return Optional.empty();
        }
    }

    public List<Product> findByNameContainingIgnoreCase(String name) {
        String sql = "SELECT * FROM products WHERE LOWER(product_name) LIKE LOWER(?) ORDER BY product_name";
        return jdbcTemplate.query(sql, productRowMapper, "%" + name + "%");
    }

    public List<Product> findAll() {
        String sql = "SELECT * FROM products ORDER BY product_name";
        return jdbcTemplate.query(sql, productRowMapper);
    }

    public List<Product> findAvailableProducts() {
        String sql = "SELECT * FROM products WHERE (quantity - reserved) > 0 ORDER BY product_name";
        return jdbcTemplate.query(sql, productRowMapper);
    }

    public Product update(Product product) {
        String sql = "UPDATE products SET product_name = ?, price = ?, quantity = ?, reserved = ?, customer_id = ?, updated_at = ? WHERE product_id = ?";
        
        LocalDateTime now = LocalDateTime.now();
        
        jdbcTemplate.update(sql,
                product.getProductName(),
                product.getPrice(),
                product.getQuantity(),
                product.getReserved(),
                product.getCustomerId(),
                Timestamp.valueOf(now),
                product.getProductId());
        
        product.setUpdatedAt(now);
        return product;
    }

    public void deleteById(Long id) {
        String sql = "DELETE FROM products WHERE product_id = ?";
        jdbcTemplate.update(sql, id);
    }

    public boolean existsById(Long id) {
        String sql = "SELECT COUNT(*) FROM products WHERE product_id = ?";
        Integer count = jdbcTemplate.queryForObject(sql, Integer.class, id);
        return count != null && count > 0;
    }

    public boolean updateQuantity(Long productId, int newQuantity) {
        String sql = "UPDATE products SET quantity = ?, updated_at = ? WHERE product_id = ?";
        int rowsAffected = jdbcTemplate.update(sql, newQuantity, Timestamp.valueOf(LocalDateTime.now()), productId);
        return rowsAffected > 0;
    }

    public boolean reserveProduct(Long productId, int reserveQuantity) {
        String sql = "UPDATE products SET reserved = reserved + ?, updated_at = ? WHERE product_id = ? AND (quantity - reserved) >= ?";
        int rowsAffected = jdbcTemplate.update(sql, reserveQuantity, Timestamp.valueOf(LocalDateTime.now()), productId, reserveQuantity);
        return rowsAffected > 0;
    }

    public boolean releaseReservation(Long productId, int releaseQuantity) {
        String sql = "UPDATE products SET reserved = reserved - ?, updated_at = ? WHERE product_id = ? AND reserved >= ?";
        int rowsAffected = jdbcTemplate.update(sql, releaseQuantity, Timestamp.valueOf(LocalDateTime.now()), productId, releaseQuantity);
        return rowsAffected > 0;
    }
}
