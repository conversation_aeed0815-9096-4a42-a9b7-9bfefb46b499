package com.grocery.repository;

import com.grocery.model.Order;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.EmptyResultDataAccessException;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.jdbc.support.GeneratedKeyHolder;
import org.springframework.jdbc.support.KeyHolder;
import org.springframework.stereotype.Repository;

import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Repository
public class OrderRepository {

    @Autowired
    private JdbcTemplate jdbcTemplate;

    private final RowMapper<Order> orderRowMapper = new RowMapper<Order>() {
        @Override
        public Order mapRow(ResultSet rs, int rowNum) throws SQLException {
            Order order = new Order();
            order.setOrderId(rs.getLong("order_id"));
            order.setCustomerId(rs.getLong("customer_id"));
            order.setProductId(rs.getLong("product_id"));
            order.setOrderAmount(rs.getBigDecimal("order_amount"));
            order.setQuantity(rs.getInt("quantity"));
            order.setStatus(rs.getString("status"));
            
            Timestamp orderDate = rs.getTimestamp("order_date");
            if (orderDate != null) {
                order.setOrderDate(orderDate.toLocalDateTime());
            }
            
            return order;
        }
    };

    private final RowMapper<Order> orderWithDetailsRowMapper = new RowMapper<Order>() {
        @Override
        public Order mapRow(ResultSet rs, int rowNum) throws SQLException {
            Order order = new Order();
            order.setOrderId(rs.getLong("order_id"));
            order.setCustomerId(rs.getLong("customer_id"));
            order.setProductId(rs.getLong("product_id"));
            order.setOrderAmount(rs.getBigDecimal("order_amount"));
            order.setQuantity(rs.getInt("quantity"));
            order.setStatus(rs.getString("status"));
            
            Timestamp orderDate = rs.getTimestamp("order_date");
            if (orderDate != null) {
                order.setOrderDate(orderDate.toLocalDateTime());
            }
            
            // Additional fields from joins
            order.setCustomerName(rs.getString("customer_name"));
            order.setProductName(rs.getString("product_name"));
            
            return order;
        }
    };

    public Order save(Order order) {
        String sql = "INSERT INTO orders (customer_id, product_id, order_date, order_amount, quantity, status) VALUES (?, ?, ?, ?, ?, ?)";
        
        KeyHolder keyHolder = new GeneratedKeyHolder();
        LocalDateTime now = LocalDateTime.now();
        
        jdbcTemplate.update(connection -> {
            PreparedStatement ps = connection.prepareStatement(sql, Statement.RETURN_GENERATED_KEYS);
            ps.setLong(1, order.getCustomerId());
            ps.setLong(2, order.getProductId());
            ps.setTimestamp(3, Timestamp.valueOf(now));
            ps.setBigDecimal(4, order.getOrderAmount());
            ps.setInt(5, order.getQuantity());
            ps.setString(6, order.getStatus() != null ? order.getStatus() : "PENDING");
            return ps;
        }, keyHolder);
        
        order.setOrderId(keyHolder.getKey().longValue());
        order.setOrderDate(now);
        
        return order;
    }

    public Optional<Order> findById(Long id) {
        String sql = "SELECT * FROM orders WHERE order_id = ?";
        try {
            Order order = jdbcTemplate.queryForObject(sql, orderRowMapper, id);
            return Optional.of(order);
        } catch (EmptyResultDataAccessException e) {
            return Optional.empty();
        }
    }

    public List<Order> findByCustomerId(Long customerId) {
        String sql = """
            SELECT o.order_id, o.customer_id, o.product_id, o.order_date, o.order_amount, o.quantity, o.status,
                   c.full_name as customer_name, p.product_name
            FROM orders o
            JOIN customers c ON o.customer_id = c.customer_id
            JOIN products p ON o.product_id = p.product_id
            WHERE o.customer_id = ?
            ORDER BY o.order_date DESC
            """;
        return jdbcTemplate.query(sql, orderWithDetailsRowMapper, customerId);
    }

    public List<Order> findAll() {
        String sql = """
            SELECT o.order_id, o.customer_id, o.product_id, o.order_date, o.order_amount, o.quantity, o.status,
                   c.full_name as customer_name, p.product_name
            FROM orders o
            JOIN customers c ON o.customer_id = c.customer_id
            JOIN products p ON o.product_id = p.product_id
            ORDER BY o.order_date DESC
            """;
        return jdbcTemplate.query(sql, orderWithDetailsRowMapper);
    }

    public List<Order> findByCustomerIdWithDetails(Long customerId) {
        String sql = """
            SELECT o.order_id, o.customer_id, o.product_id, o.order_date, o.order_amount, o.quantity, o.status,
                   c.full_name as customer_name, p.product_name
            FROM orders o
            JOIN customers c ON o.customer_id = c.customer_id
            JOIN products p ON o.product_id = p.product_id
            WHERE o.customer_id = ?
            ORDER BY o.order_date DESC
            """;
        return jdbcTemplate.query(sql, orderWithDetailsRowMapper, customerId);
    }

    public Order update(Order order) {
        String sql = "UPDATE orders SET customer_id = ?, product_id = ?, order_amount = ?, quantity = ?, status = ? WHERE order_id = ?";
        
        jdbcTemplate.update(sql,
                order.getCustomerId(),
                order.getProductId(),
                order.getOrderAmount(),
                order.getQuantity(),
                order.getStatus(),
                order.getOrderId());
        
        return order;
    }

    public void deleteById(Long id) {
        String sql = "DELETE FROM orders WHERE order_id = ?";
        jdbcTemplate.update(sql, id);
    }

    public boolean existsById(Long id) {
        String sql = "SELECT COUNT(*) FROM orders WHERE order_id = ?";
        Integer count = jdbcTemplate.queryForObject(sql, Integer.class, id);
        return count != null && count > 0;
    }

    public List<Order> findByDateRange(LocalDateTime startDate, LocalDateTime endDate) {
        String sql = """
            SELECT o.order_id, o.customer_id, o.product_id, o.order_date, o.order_amount, o.quantity, o.status,
                   c.full_name as customer_name, p.product_name
            FROM orders o
            JOIN customers c ON o.customer_id = c.customer_id
            JOIN products p ON o.product_id = p.product_id
            WHERE o.order_date BETWEEN ? AND ?
            ORDER BY o.order_date DESC
            """;
        return jdbcTemplate.query(sql, orderWithDetailsRowMapper, 
                Timestamp.valueOf(startDate), Timestamp.valueOf(endDate));
    }

    public boolean updateStatus(Long orderId, String status) {
        String sql = "UPDATE orders SET status = ? WHERE order_id = ?";
        int rowsAffected = jdbcTemplate.update(sql, status, orderId);
        return rowsAffected > 0;
    }
}
