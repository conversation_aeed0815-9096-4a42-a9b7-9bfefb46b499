package com.grocery.repository;

import com.grocery.model.Customer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.EmptyResultDataAccessException;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.jdbc.support.GeneratedKeyHolder;
import org.springframework.jdbc.support.KeyHolder;
import org.springframework.stereotype.Repository;

import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Repository
public class CustomerRepository {

    @Autowired
    private JdbcTemplate jdbcTemplate;

    private final RowMapper<Customer> customerRowMapper = new RowMapper<Customer>() {
        @Override
        public Customer mapRow(ResultSet rs, int rowNum) throws SQLException {
            Customer customer = new Customer();
            customer.setCustomerId(rs.getLong("customer_id"));
            customer.setFullName(rs.getString("full_name"));
            customer.setEmail(rs.getString("email"));
            customer.setPassword(rs.getString("password"));
            customer.setAddress(rs.getString("address"));
            customer.setContactNumber(rs.getString("contact_number"));
            
            Timestamp createdAt = rs.getTimestamp("created_at");
            if (createdAt != null) {
                customer.setCreatedAt(createdAt.toLocalDateTime());
            }
            
            Timestamp updatedAt = rs.getTimestamp("updated_at");
            if (updatedAt != null) {
                customer.setUpdatedAt(updatedAt.toLocalDateTime());
            }
            
            return customer;
        }
    };

    public Customer save(Customer customer) {
        String sql = "INSERT INTO customers (full_name, email, password, address, contact_number, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?)";
        
        KeyHolder keyHolder = new GeneratedKeyHolder();
        LocalDateTime now = LocalDateTime.now();
        
        jdbcTemplate.update(connection -> {
            PreparedStatement ps = connection.prepareStatement(sql, Statement.RETURN_GENERATED_KEYS);
            ps.setString(1, customer.getFullName());
            ps.setString(2, customer.getEmail());
            ps.setString(3, customer.getPassword());
            ps.setString(4, customer.getAddress());
            ps.setString(5, customer.getContactNumber());
            ps.setTimestamp(6, Timestamp.valueOf(now));
            ps.setTimestamp(7, Timestamp.valueOf(now));
            return ps;
        }, keyHolder);
        
        customer.setCustomerId(keyHolder.getKey().longValue());
        customer.setCreatedAt(now);
        customer.setUpdatedAt(now);
        
        return customer;
    }

    public Optional<Customer> findById(Long id) {
        String sql = "SELECT * FROM customers WHERE customer_id = ?";
        try {
            Customer customer = jdbcTemplate.queryForObject(sql, customerRowMapper, id);
            return Optional.of(customer);
        } catch (EmptyResultDataAccessException e) {
            return Optional.empty();
        }
    }

    public Optional<Customer> findByEmail(String email) {
        String sql = "SELECT * FROM customers WHERE email = ?";
        try {
            Customer customer = jdbcTemplate.queryForObject(sql, customerRowMapper, email);
            return Optional.of(customer);
        } catch (EmptyResultDataAccessException e) {
            return Optional.empty();
        }
    }

    public List<Customer> findByNameContainingIgnoreCase(String name) {
        String sql = "SELECT * FROM customers WHERE LOWER(full_name) LIKE LOWER(?) ORDER BY full_name";
        return jdbcTemplate.query(sql, customerRowMapper, "%" + name + "%");
    }

    public Customer update(Customer customer) {
        String sql = "UPDATE customers SET full_name = ?, email = ?, password = ?, address = ?, contact_number = ?, updated_at = ? WHERE customer_id = ?";
        
        LocalDateTime now = LocalDateTime.now();
        
        jdbcTemplate.update(sql,
                customer.getFullName(),
                customer.getEmail(),
                customer.getPassword(),
                customer.getAddress(),
                customer.getContactNumber(),
                Timestamp.valueOf(now),
                customer.getCustomerId());
        
        customer.setUpdatedAt(now);
        return customer;
    }

    public boolean existsByEmail(String email) {
        String sql = "SELECT COUNT(*) FROM customers WHERE email = ?";
        Integer count = jdbcTemplate.queryForObject(sql, Integer.class, email);
        return count != null && count > 0;
    }

    public boolean existsByEmailAndNotId(String email, Long customerId) {
        String sql = "SELECT COUNT(*) FROM customers WHERE email = ? AND customer_id != ?";
        Integer count = jdbcTemplate.queryForObject(sql, Integer.class, email, customerId);
        return count != null && count > 0;
    }

    public List<Customer> findAll() {
        String sql = "SELECT * FROM customers ORDER BY full_name";
        return jdbcTemplate.query(sql, customerRowMapper);
    }

    public void deleteById(Long id) {
        String sql = "DELETE FROM customers WHERE customer_id = ?";
        jdbcTemplate.update(sql, id);
    }
}
