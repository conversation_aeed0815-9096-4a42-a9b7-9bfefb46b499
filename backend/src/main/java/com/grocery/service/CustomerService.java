package com.grocery.service;

import com.grocery.model.Customer;
import com.grocery.repository.CustomerRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;
import java.util.regex.Pattern;

@Service
@Transactional
public class CustomerService {

    @Autowired
    private CustomerRepository customerRepository;

    @Autowired
    private PasswordEncoder passwordEncoder;

    private static final Pattern EMAIL_PATTERN = Pattern.compile(
        "^[a-zA-Z0-9_+&*-]+(?:\\.[a-zA-Z0-9_+&*-]+)*@(?:[a-zA-Z0-9-]+\\.)+[a-zA-Z]{2,7}$"
    );

    private static final Pattern PASSWORD_PATTERN = Pattern.compile(
        "^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)(?=.*[@$!%*?&])[A-Za-z\\d@$!%*?&]{8,}$"
    );

    private static final Pattern PHONE_PATTERN = Pattern.compile("^\\d{10}$");

    /**
     * US002: Customer Registration
     */
    public Customer registerCustomer(Customer customer) throws Exception {
        // Validate input
        validateCustomerInput(customer);
        
        // Check if email already exists
        if (customerRepository.existsByEmail(customer.getEmail())) {
            throw new Exception("Email address is already registered");
        }
        
        // Encrypt password
        customer.setPassword(passwordEncoder.encode(customer.getPassword()));
        
        // Save customer
        return customerRepository.save(customer);
    }

    /**
     * US003: Update Customer Details
     */
    public Customer updateCustomer(Long customerId, Customer updatedCustomer) throws Exception {
        // Find existing customer
        Optional<Customer> existingCustomerOpt = customerRepository.findById(customerId);
        if (existingCustomerOpt.isEmpty()) {
            throw new Exception("Customer not found");
        }
        
        Customer existingCustomer = existingCustomerOpt.get();
        
        // Validate updated information
        validateCustomerInput(updatedCustomer);
        
        // Check if email is being changed and if new email already exists
        if (!existingCustomer.getEmail().equals(updatedCustomer.getEmail())) {
            if (customerRepository.existsByEmailAndNotId(updatedCustomer.getEmail(), customerId)) {
                throw new Exception("Email address is already registered");
            }
        }
        
        // Update fields
        existingCustomer.setFullName(updatedCustomer.getFullName());
        existingCustomer.setEmail(updatedCustomer.getEmail());
        existingCustomer.setAddress(updatedCustomer.getAddress());
        existingCustomer.setContactNumber(updatedCustomer.getContactNumber());
        
        // Update password if provided
        if (updatedCustomer.getPassword() != null && !updatedCustomer.getPassword().isEmpty()) {
            if (!PASSWORD_PATTERN.matcher(updatedCustomer.getPassword()).matches()) {
                throw new Exception("Password must be at least 8 characters long and contain at least one uppercase letter, one lowercase letter, one digit, and one special character");
            }
            existingCustomer.setPassword(passwordEncoder.encode(updatedCustomer.getPassword()));
        }
        
        return customerRepository.update(existingCustomer);
    }

    /**
     * US005: Search Customer by Name (Admin only)
     */
    public List<Customer> searchCustomersByName(String customerName) throws Exception {
        if (customerName == null || customerName.trim().isEmpty()) {
            throw new Exception("Customer name cannot be empty");
        }
        
        List<Customer> customers = customerRepository.findByNameContainingIgnoreCase(customerName.trim());
        
        if (customers.isEmpty()) {
            throw new Exception("Customer not found");
        }
        
        // Mask passwords for security
        customers.forEach(customer -> customer.setPassword("***ENCRYPTED***"));
        
        return customers;
    }

    public Optional<Customer> findById(Long customerId) {
        return customerRepository.findById(customerId);
    }

    public Optional<Customer> findByEmail(String email) {
        return customerRepository.findByEmail(email);
    }

    public List<Customer> findAllCustomers() {
        List<Customer> customers = customerRepository.findAll();
        // Mask passwords for security
        customers.forEach(customer -> customer.setPassword("***ENCRYPTED***"));
        return customers;
    }

    public boolean authenticateCustomer(String email, String password) {
        Optional<Customer> customerOpt = customerRepository.findByEmail(email);
        if (customerOpt.isPresent()) {
            return passwordEncoder.matches(password, customerOpt.get().getPassword());
        }
        return false;
    }

    private void validateCustomerInput(Customer customer) throws Exception {
        // Validate full name
        if (customer.getFullName() == null || customer.getFullName().trim().length() < 2) {
            throw new Exception("Full name must be at least 2 characters long");
        }
        if (customer.getFullName().length() > 100) {
            throw new Exception("Full name cannot exceed 100 characters");
        }

        // Validate email
        if (customer.getEmail() == null || !EMAIL_PATTERN.matcher(customer.getEmail()).matches()) {
            throw new Exception("Please provide a valid email address");
        }

        // Validate password (only for new registrations or password updates)
        if (customer.getPassword() != null && !customer.getPassword().isEmpty()) {
            if (!PASSWORD_PATTERN.matcher(customer.getPassword()).matches()) {
                throw new Exception("Password must be at least 8 characters long and contain at least one uppercase letter, one lowercase letter, one digit, and one special character");
            }
        }

        // Validate address
        if (customer.getAddress() == null || customer.getAddress().trim().isEmpty()) {
            throw new Exception("Address is required");
        }

        // Validate contact number
        if (customer.getContactNumber() == null || !PHONE_PATTERN.matcher(customer.getContactNumber()).matches()) {
            throw new Exception("Contact number must be exactly 10 digits");
        }
    }

    public void deleteCustomer(Long customerId) throws Exception {
        if (!customerRepository.findById(customerId).isPresent()) {
            throw new Exception("Customer not found");
        }
        customerRepository.deleteById(customerId);
    }
}
