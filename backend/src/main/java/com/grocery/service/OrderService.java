package com.grocery.service;

import com.grocery.model.Order;
import com.grocery.model.Product;
import com.grocery.model.Customer;
import com.grocery.repository.OrderRepository;
import com.grocery.repository.ProductRepository;
import com.grocery.repository.CustomerRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Service
@Transactional
public class OrderService {

    @Autowired
    private OrderRepository orderRepository;

    @Autowired
    private ProductRepository productRepository;

    @Autowired
    private CustomerRepository customerRepository;

    @Autowired
    private ProductService productService;

    /**
     * US004: Get Customer Order Details
     */
    public List<Order> getCustomerOrderDetails(Long customerId) throws Exception {
        // Verify customer exists
        Optional<Customer> customerOpt = customerRepository.findById(customerId);
        if (customerOpt.isEmpty()) {
            throw new Exception("Customer not found");
        }
        
        return orderRepository.findByCustomerIdWithDetails(customerId);
    }

    /**
     * Create a new order
     */
    public Order createOrder(Long customerId, Long productId, Integer quantity) throws Exception {
        // Validate customer exists
        Optional<Customer> customerOpt = customerRepository.findById(customerId);
        if (customerOpt.isEmpty()) {
            throw new Exception("Customer not found");
        }

        // Validate product exists and is available
        Optional<Product> productOpt = productRepository.findById(productId);
        if (productOpt.isEmpty()) {
            throw new Exception("Product not found");
        }

        Product product = productOpt.get();
        
        // Check if sufficient quantity is available
        if (product.getAvailableQuantity() < quantity) {
            throw new Exception("Insufficient product quantity available. Available: " + product.getAvailableQuantity());
        }

        // Calculate order amount
        BigDecimal orderAmount = product.getPrice().multiply(new BigDecimal(quantity));

        // Reserve the product quantity
        boolean reserved = productService.reserveProduct(productId, quantity);
        if (!reserved) {
            throw new Exception("Failed to reserve product quantity");
        }

        try {
            // Create order
            Order order = new Order(customerId, productId, orderAmount, quantity);
            order.setStatus("CONFIRMED");
            
            Order savedOrder = orderRepository.save(order);
            
            // Update product quantity (reduce available quantity)
            int newQuantity = product.getQuantity() - quantity;
            productService.updateQuantity(productId, newQuantity);
            
            // Release reservation since we've updated the actual quantity
            productService.releaseReservation(productId, quantity);
            
            return savedOrder;
            
        } catch (Exception e) {
            // If order creation fails, release the reservation
            productService.releaseReservation(productId, quantity);
            throw new Exception("Failed to create order: " + e.getMessage());
        }
    }

    public Optional<Order> findById(Long orderId) {
        return orderRepository.findById(orderId);
    }

    public List<Order> findAllOrders() {
        return orderRepository.findAll();
    }

    public List<Order> findOrdersByCustomerId(Long customerId) {
        return orderRepository.findByCustomerId(customerId);
    }

    public Order updateOrderStatus(Long orderId, String status) throws Exception {
        Optional<Order> orderOpt = orderRepository.findById(orderId);
        if (orderOpt.isEmpty()) {
            throw new Exception("Order not found");
        }

        Order order = orderOpt.get();
        order.setStatus(status);
        
        return orderRepository.update(order);
    }

    public void cancelOrder(Long orderId) throws Exception {
        Optional<Order> orderOpt = orderRepository.findById(orderId);
        if (orderOpt.isEmpty()) {
            throw new Exception("Order not found");
        }

        Order order = orderOpt.get();
        
        // If order is not yet shipped, restore product quantity
        if (!"SHIPPED".equals(order.getStatus()) && !"DELIVERED".equals(order.getStatus())) {
            Optional<Product> productOpt = productRepository.findById(order.getProductId());
            if (productOpt.isPresent()) {
                Product product = productOpt.get();
                int restoredQuantity = product.getQuantity() + order.getQuantity();
                productService.updateQuantity(order.getProductId(), restoredQuantity);
            }
        }

        // Update order status to cancelled
        order.setStatus("CANCELLED");
        orderRepository.update(order);
    }

    public void deleteOrder(Long orderId) throws Exception {
        if (!orderRepository.existsById(orderId)) {
            throw new Exception("Order not found");
        }
        
        orderRepository.deleteById(orderId);
    }

    public List<Order> findOrdersByDateRange(LocalDateTime startDate, LocalDateTime endDate) {
        return orderRepository.findByDateRange(startDate, endDate);
    }

    public BigDecimal calculateTotalOrderAmount(Long customerId) throws Exception {
        List<Order> orders = orderRepository.findByCustomerId(customerId);
        
        return orders.stream()
                .filter(order -> !"CANCELLED".equals(order.getStatus()))
                .map(Order::getOrderAmount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    public Order updateOrder(Long orderId, Order updatedOrder) throws Exception {
        Optional<Order> existingOrderOpt = orderRepository.findById(orderId);
        if (existingOrderOpt.isEmpty()) {
            throw new Exception("Order not found");
        }

        Order existingOrder = existingOrderOpt.get();
        
        // Validate the updated order
        validateOrderInput(updatedOrder);
        
        // Update fields
        existingOrder.setCustomerId(updatedOrder.getCustomerId());
        existingOrder.setProductId(updatedOrder.getProductId());
        existingOrder.setOrderAmount(updatedOrder.getOrderAmount());
        existingOrder.setQuantity(updatedOrder.getQuantity());
        existingOrder.setStatus(updatedOrder.getStatus());
        
        return orderRepository.update(existingOrder);
    }

    private void validateOrderInput(Order order) throws Exception {
        if (order.getCustomerId() == null) {
            throw new Exception("Customer ID is required");
        }
        
        if (order.getProductId() == null) {
            throw new Exception("Product ID is required");
        }
        
        if (order.getOrderAmount() == null || order.getOrderAmount().compareTo(BigDecimal.ZERO) <= 0) {
            throw new Exception("Order amount must be greater than 0");
        }
        
        if (order.getQuantity() == null || order.getQuantity() <= 0) {
            throw new Exception("Quantity must be greater than 0");
        }
    }

    public boolean hasCustomerOrderedProduct(Long customerId, Long productId) {
        List<Order> orders = orderRepository.findByCustomerId(customerId);
        return orders.stream()
                .anyMatch(order -> order.getProductId().equals(productId) && 
                         !"CANCELLED".equals(order.getStatus()));
    }
}
