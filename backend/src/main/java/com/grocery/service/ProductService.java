package com.grocery.service;

import com.grocery.model.Product;
import com.grocery.repository.ProductRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;

@Service
@Transactional
public class ProductService {

    @Autowired
    private ProductRepository productRepository;

    /**
     * US007: Register Product (Admin only)
     */
    public Product registerProduct(Product product) throws Exception {
        // Validate product input
        validateProductInput(product);
        
        // Set default values
        if (product.getReserved() == null) {
            product.setReserved(0);
        }
        
        // Save product
        return productRepository.save(product);
    }

    /**
     * US008: Update Product (Admin only)
     */
    public Product updateProduct(Long productId, Product updatedProduct) throws Exception {
        // Find existing product
        Optional<Product> existingProductOpt = productRepository.findById(productId);
        if (existingProductOpt.isEmpty()) {
            throw new Exception("Product not found");
        }
        
        Product existingProduct = existingProductOpt.get();
        
        // Validate updated information
        validateProductInput(updatedProduct);
        
        // Update fields
        existingProduct.setProductName(updatedProduct.getProductName());
        existingProduct.setPrice(updatedProduct.getPrice());
        existingProduct.setQuantity(updatedProduct.getQuantity());
        
        if (updatedProduct.getReserved() != null) {
            existingProduct.setReserved(updatedProduct.getReserved());
        }
        
        if (updatedProduct.getCustomerId() != null) {
            existingProduct.setCustomerId(updatedProduct.getCustomerId());
        }
        
        return productRepository.update(existingProduct);
    }

    /**
     * US009: Delete Product (Admin only)
     */
    public void deleteProduct(Long productId) throws Exception {
        if (!productRepository.existsById(productId)) {
            throw new Exception("Product not found");
        }
        
        productRepository.deleteById(productId);
    }

    /**
     * US006: Search Product by Name (Customer)
     */
    public List<Product> searchProductsByName(String productName) throws Exception {
        if (productName == null || productName.trim().isEmpty()) {
            throw new Exception("Product name cannot be empty");
        }
        
        List<Product> products = productRepository.findByNameContainingIgnoreCase(productName.trim());
        
        if (products.isEmpty()) {
            throw new Exception("Product not found");
        }
        
        return products;
    }

    public Optional<Product> findById(Long productId) {
        return productRepository.findById(productId);
    }

    public List<Product> findAllProducts() {
        return productRepository.findAll();
    }

    public List<Product> findAvailableProducts() {
        return productRepository.findAvailableProducts();
    }

    public boolean reserveProduct(Long productId, int quantity) throws Exception {
        Optional<Product> productOpt = productRepository.findById(productId);
        if (productOpt.isEmpty()) {
            throw new Exception("Product not found");
        }
        
        Product product = productOpt.get();
        if (product.getAvailableQuantity() < quantity) {
            throw new Exception("Insufficient product quantity available");
        }
        
        return productRepository.reserveProduct(productId, quantity);
    }

    public boolean releaseReservation(Long productId, int quantity) throws Exception {
        Optional<Product> productOpt = productRepository.findById(productId);
        if (productOpt.isEmpty()) {
            throw new Exception("Product not found");
        }
        
        Product product = productOpt.get();
        if (product.getReserved() < quantity) {
            throw new Exception("Cannot release more than reserved quantity");
        }
        
        return productRepository.releaseReservation(productId, quantity);
    }

    public boolean updateQuantity(Long productId, int newQuantity) throws Exception {
        if (newQuantity < 0) {
            throw new Exception("Quantity cannot be negative");
        }
        
        Optional<Product> productOpt = productRepository.findById(productId);
        if (productOpt.isEmpty()) {
            throw new Exception("Product not found");
        }
        
        Product product = productOpt.get();
        if (newQuantity < product.getReserved()) {
            throw new Exception("New quantity cannot be less than reserved quantity");
        }
        
        return productRepository.updateQuantity(productId, newQuantity);
    }

    private void validateProductInput(Product product) throws Exception {
        // Validate product name
        if (product.getProductName() == null || product.getProductName().trim().length() < 2) {
            throw new Exception("Product name must be at least 2 characters long");
        }
        if (product.getProductName().length() > 100) {
            throw new Exception("Product name cannot exceed 100 characters");
        }

        // Validate price
        if (product.getPrice() == null || product.getPrice().compareTo(BigDecimal.ZERO) <= 0) {
            throw new Exception("Price must be greater than 0");
        }

        // Validate quantity
        if (product.getQuantity() == null || product.getQuantity() < 0) {
            throw new Exception("Quantity cannot be negative");
        }

        // Validate reserved quantity
        if (product.getReserved() != null && product.getReserved() < 0) {
            throw new Exception("Reserved quantity cannot be negative");
        }

        // Validate that reserved quantity doesn't exceed total quantity
        if (product.getReserved() != null && product.getQuantity() != null && 
            product.getReserved() > product.getQuantity()) {
            throw new Exception("Reserved quantity cannot exceed total quantity");
        }
    }

    public boolean isProductAvailable(Long productId, int requestedQuantity) {
        Optional<Product> productOpt = productRepository.findById(productId);
        if (productOpt.isEmpty()) {
            return false;
        }
        
        Product product = productOpt.get();
        return product.getAvailableQuantity() >= requestedQuantity;
    }

    public Product getProductWithAvailability(Long productId) throws Exception {
        Optional<Product> productOpt = productRepository.findById(productId);
        if (productOpt.isEmpty()) {
            throw new Exception("Product not found");
        }
        
        return productOpt.get();
    }
}
