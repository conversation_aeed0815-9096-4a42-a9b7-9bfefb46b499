package com.grocery.controller;

import com.grocery.model.Order;
import com.grocery.service.OrderService;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

@RestController
@RequestMapping("/orders")
@CrossOrigin(origins = {"http://localhost:4200", "http://localhost:3000"})
public class OrderController {

    @Autowired
    private OrderService orderService;

    /**
     * Create a new order
     */
    @PostMapping("/create")
    @PreAuthorize("hasRole('CUSTOMER') or hasRole('ADMIN')")
    public ResponseEntity<?> createOrder(@RequestBody CreateOrderRequest orderRequest,
                                       HttpServletRequest request) {
        try {
            // For customers, ensure they can only create orders for themselves
            Long requestUserId = (Long) request.getAttribute("userId");
            String userRole = (String) request.getAttribute("userRole");
            
            Long customerId = orderRequest.getCustomerId();
            if (!"ADMIN".equals(userRole) && !customerId.equals(requestUserId)) {
                Map<String, String> errorResponse = new HashMap<>();
                errorResponse.put("error", "You can only create orders for yourself");
                return ResponseEntity.forbidden().body(errorResponse);
            }
            
            Order createdOrder = orderService.createOrder(
                orderRequest.getCustomerId(),
                orderRequest.getProductId(),
                orderRequest.getQuantity()
            );
            
            Map<String, Object> response = new HashMap<>();
            response.put("message", "Order created successfully");
            response.put("order", createdOrder);
            response.put("orderId", createdOrder.getOrderId());
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            Map<String, String> errorResponse = new HashMap<>();
            errorResponse.put("error", e.getMessage());
            return ResponseEntity.badRequest().body(errorResponse);
        }
    }

    /**
     * Get order by ID
     */
    @GetMapping("/{orderId}")
    @PreAuthorize("hasRole('CUSTOMER') or hasRole('ADMIN')")
    public ResponseEntity<?> getOrderById(@PathVariable Long orderId,
                                        HttpServletRequest request) {
        try {
            Optional<Order> orderOpt = orderService.findById(orderId);
            if (orderOpt.isEmpty()) {
                Map<String, String> errorResponse = new HashMap<>();
                errorResponse.put("error", "Order not found");
                return ResponseEntity.notFound().build();
            }
            
            Order order = orderOpt.get();
            
            // For customers, ensure they can only view their own orders
            Long requestUserId = (Long) request.getAttribute("userId");
            String userRole = (String) request.getAttribute("userRole");
            
            if (!"ADMIN".equals(userRole) && !order.getCustomerId().equals(requestUserId)) {
                Map<String, String> errorResponse = new HashMap<>();
                errorResponse.put("error", "You can only view your own orders");
                return ResponseEntity.forbidden().body(errorResponse);
            }
            
            Map<String, Object> response = new HashMap<>();
            response.put("message", "Order details retrieved successfully");
            response.put("order", order);
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            Map<String, String> errorResponse = new HashMap<>();
            errorResponse.put("error", e.getMessage());
            return ResponseEntity.badRequest().body(errorResponse);
        }
    }

    /**
     * Get all orders (Admin only)
     */
    @GetMapping
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<?> getAllOrders() {
        try {
            List<Order> orders = orderService.findAllOrders();
            
            Map<String, Object> response = new HashMap<>();
            response.put("message", "All orders retrieved successfully");
            response.put("orders", orders);
            response.put("totalOrders", orders.size());
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            Map<String, String> errorResponse = new HashMap<>();
            errorResponse.put("error", e.getMessage());
            return ResponseEntity.badRequest().body(errorResponse);
        }
    }

    /**
     * Update order status
     */
    @PutMapping("/{orderId}/status")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<?> updateOrderStatus(@PathVariable Long orderId,
                                             @RequestParam String status) {
        try {
            Order updatedOrder = orderService.updateOrderStatus(orderId, status);
            
            Map<String, Object> response = new HashMap<>();
            response.put("message", "Order status updated successfully");
            response.put("order", updatedOrder);
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            Map<String, String> errorResponse = new HashMap<>();
            errorResponse.put("error", e.getMessage());
            return ResponseEntity.badRequest().body(errorResponse);
        }
    }

    /**
     * Cancel order
     */
    @PutMapping("/{orderId}/cancel")
    @PreAuthorize("hasRole('CUSTOMER') or hasRole('ADMIN')")
    public ResponseEntity<?> cancelOrder(@PathVariable Long orderId,
                                       HttpServletRequest request) {
        try {
            // For customers, ensure they can only cancel their own orders
            Optional<Order> orderOpt = orderService.findById(orderId);
            if (orderOpt.isEmpty()) {
                Map<String, String> errorResponse = new HashMap<>();
                errorResponse.put("error", "Order not found");
                return ResponseEntity.notFound().build();
            }
            
            Order order = orderOpt.get();
            Long requestUserId = (Long) request.getAttribute("userId");
            String userRole = (String) request.getAttribute("userRole");
            
            if (!"ADMIN".equals(userRole) && !order.getCustomerId().equals(requestUserId)) {
                Map<String, String> errorResponse = new HashMap<>();
                errorResponse.put("error", "You can only cancel your own orders");
                return ResponseEntity.forbidden().body(errorResponse);
            }
            
            orderService.cancelOrder(orderId);
            
            Map<String, String> response = new HashMap<>();
            response.put("message", "Order cancelled successfully");
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            Map<String, String> errorResponse = new HashMap<>();
            errorResponse.put("error", e.getMessage());
            return ResponseEntity.badRequest().body(errorResponse);
        }
    }

    /**
     * Update order
     */
    @PutMapping("/{orderId}")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<?> updateOrder(@PathVariable Long orderId,
                                       @Valid @RequestBody Order order) {
        try {
            Order updatedOrder = orderService.updateOrder(orderId, order);
            
            Map<String, Object> response = new HashMap<>();
            response.put("message", "Order updated successfully");
            response.put("order", updatedOrder);
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            Map<String, String> errorResponse = new HashMap<>();
            errorResponse.put("error", e.getMessage());
            return ResponseEntity.badRequest().body(errorResponse);
        }
    }

    // Inner class for create order request
    public static class CreateOrderRequest {
        private Long customerId;
        private Long productId;
        private Integer quantity;

        public CreateOrderRequest() {}

        public CreateOrderRequest(Long customerId, Long productId, Integer quantity) {
            this.customerId = customerId;
            this.productId = productId;
            this.quantity = quantity;
        }

        public Long getCustomerId() {
            return customerId;
        }

        public void setCustomerId(Long customerId) {
            this.customerId = customerId;
        }

        public Long getProductId() {
            return productId;
        }

        public void setProductId(Long productId) {
            this.productId = productId;
        }

        public Integer getQuantity() {
            return quantity;
        }

        public void setQuantity(Integer quantity) {
            this.quantity = quantity;
        }
    }
}
