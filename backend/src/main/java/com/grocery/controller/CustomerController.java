package com.grocery.controller;

import com.grocery.model.Customer;
import com.grocery.model.Order;
import com.grocery.service.CustomerService;
import com.grocery.service.OrderService;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

@RestController
@RequestMapping("/customers")
@CrossOrigin(origins = {"http://localhost:4200", "http://localhost:3000"})
public class CustomerController {

    @Autowired
    private CustomerService customerService;

    @Autowired
    private OrderService orderService;

    /**
     * US002: Customer Registration
     */
    @PostMapping("/register")
    public ResponseEntity<?> registerCustomer(@Valid @RequestBody Customer customer) {
        try {
            Customer registeredCustomer = customerService.registerCustomer(customer);
            
            // Remove password from response
            registeredCustomer.setPassword(null);
            
            Map<String, Object> response = new HashMap<>();
            response.put("message", "Customer registered successfully");
            response.put("customer", registeredCustomer);
            response.put("customerId", registeredCustomer.getCustomerId());
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            Map<String, String> errorResponse = new HashMap<>();
            errorResponse.put("error", e.getMessage());
            return ResponseEntity.badRequest().body(errorResponse);
        }
    }

    /**
     * US003: Update Customer Details
     */
    @PutMapping("/update/{customerId}")
    @PreAuthorize("hasRole('ADMIN') or hasRole('CUSTOMER')")
    public ResponseEntity<?> updateCustomer(@PathVariable Long customerId,
                                          @Valid @RequestBody Customer customer,
                                          HttpServletRequest request) {
        try {
            // Verify user can only update their own details (unless admin)
            Long requestUserId = (Long) request.getAttribute("userId");
            String userRole = (String) request.getAttribute("userRole");
            
            if (!"ADMIN".equals(userRole) && !customerId.equals(requestUserId)) {
                Map<String, String> errorResponse = new HashMap<>();
                errorResponse.put("error", "You can only update your own details");
                return ResponseEntity.forbidden().body(errorResponse);
            }
            
            Customer updatedCustomer = customerService.updateCustomer(customerId, customer);
            
            // Remove password from response
            updatedCustomer.setPassword(null);
            
            Map<String, Object> response = new HashMap<>();
            response.put("message", "Customer details updated successfully");
            response.put("customer", updatedCustomer);
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            Map<String, String> errorResponse = new HashMap<>();
            errorResponse.put("error", e.getMessage());
            return ResponseEntity.badRequest().body(errorResponse);
        }
    }

    /**
     * US004: Get Customer Order Details
     */
    @GetMapping("/{customerId}/orders")
    @PreAuthorize("hasRole('ADMIN') or (hasRole('CUSTOMER') and #customerId == authentication.principal)")
    public ResponseEntity<?> getCustomerOrderDetails(@PathVariable Long customerId,
                                                    HttpServletRequest request) {
        try {
            // Verify user can only view their own orders (unless admin)
            Long requestUserId = (Long) request.getAttribute("userId");
            String userRole = (String) request.getAttribute("userRole");
            
            if (!"ADMIN".equals(userRole) && !customerId.equals(requestUserId)) {
                Map<String, String> errorResponse = new HashMap<>();
                errorResponse.put("error", "You can only view your own orders");
                return ResponseEntity.forbidden().body(errorResponse);
            }
            
            List<Order> orders = orderService.getCustomerOrderDetails(customerId);
            
            Map<String, Object> response = new HashMap<>();
            response.put("message", "Customer order details retrieved successfully");
            response.put("orders", orders);
            response.put("totalOrders", orders.size());
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            Map<String, String> errorResponse = new HashMap<>();
            errorResponse.put("error", e.getMessage());
            return ResponseEntity.badRequest().body(errorResponse);
        }
    }

    /**
     * US005: Search Customer by Name (Admin only)
     */
    @GetMapping("/search")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<?> searchCustomersByName(@RequestParam String name) {
        try {
            List<Customer> customers = customerService.searchCustomersByName(name);
            
            Map<String, Object> response = new HashMap<>();
            response.put("message", "Customer search completed successfully");
            response.put("customers", customers);
            response.put("totalFound", customers.size());
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            Map<String, String> errorResponse = new HashMap<>();
            errorResponse.put("error", e.getMessage());
            return ResponseEntity.badRequest().body(errorResponse);
        }
    }

    /**
     * Get customer by ID (Admin only or own details)
     */
    @GetMapping("/{customerId}")
    @PreAuthorize("hasRole('ADMIN') or (hasRole('CUSTOMER') and #customerId == authentication.principal)")
    public ResponseEntity<?> getCustomerById(@PathVariable Long customerId,
                                           HttpServletRequest request) {
        try {
            // Verify user can only view their own details (unless admin)
            Long requestUserId = (Long) request.getAttribute("userId");
            String userRole = (String) request.getAttribute("userRole");
            
            if (!"ADMIN".equals(userRole) && !customerId.equals(requestUserId)) {
                Map<String, String> errorResponse = new HashMap<>();
                errorResponse.put("error", "You can only view your own details");
                return ResponseEntity.forbidden().body(errorResponse);
            }
            
            Optional<Customer> customerOpt = customerService.findById(customerId);
            if (customerOpt.isEmpty()) {
                Map<String, String> errorResponse = new HashMap<>();
                errorResponse.put("error", "Customer not found");
                return ResponseEntity.notFound().build();
            }
            
            Customer customer = customerOpt.get();
            customer.setPassword(null); // Remove password from response
            
            Map<String, Object> response = new HashMap<>();
            response.put("message", "Customer details retrieved successfully");
            response.put("customer", customer);
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            Map<String, String> errorResponse = new HashMap<>();
            errorResponse.put("error", e.getMessage());
            return ResponseEntity.badRequest().body(errorResponse);
        }
    }

    /**
     * Get all customers (Admin only)
     */
    @GetMapping
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<?> getAllCustomers() {
        try {
            List<Customer> customers = customerService.findAllCustomers();
            
            Map<String, Object> response = new HashMap<>();
            response.put("message", "All customers retrieved successfully");
            response.put("customers", customers);
            response.put("totalCustomers", customers.size());
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            Map<String, String> errorResponse = new HashMap<>();
            errorResponse.put("error", e.getMessage());
            return ResponseEntity.badRequest().body(errorResponse);
        }
    }
}
