package com.grocery.controller;

import com.grocery.model.Product;
import com.grocery.service.ProductService;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

@RestController
@RequestMapping("/products")
@CrossOrigin(origins = {"http://localhost:4200", "http://localhost:3000"})
public class ProductController {

    @Autowired
    private ProductService productService;

    /**
     * US006: Search Product by Name (Customer and Admin)
     */
    @GetMapping("/search")
    @PreAuthorize("hasRole('CUSTOMER') or hasRole('ADMIN')")
    public ResponseEntity<?> searchProductsByName(@RequestParam String name) {
        try {
            List<Product> products = productService.searchProductsByName(name);
            
            Map<String, Object> response = new HashMap<>();
            response.put("message", "Product search completed successfully");
            response.put("products", products);
            response.put("totalFound", products.size());
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            Map<String, String> errorResponse = new HashMap<>();
            errorResponse.put("error", e.getMessage());
            return ResponseEntity.badRequest().body(errorResponse);
        }
    }

    /**
     * US007: Register Product (Admin only)
     */
    @PostMapping("/register")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<?> registerProduct(@Valid @RequestBody Product product) {
        try {
            Product registeredProduct = productService.registerProduct(product);
            
            Map<String, Object> response = new HashMap<>();
            response.put("message", "Product registered successfully");
            response.put("product", registeredProduct);
            response.put("productId", registeredProduct.getProductId());
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            Map<String, String> errorResponse = new HashMap<>();
            errorResponse.put("error", e.getMessage());
            return ResponseEntity.badRequest().body(errorResponse);
        }
    }

    /**
     * US008: Update Product (Admin only)
     */
    @PutMapping("/update/{productId}")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<?> updateProduct(@PathVariable Long productId, 
                                         @Valid @RequestBody Product product) {
        try {
            Product updatedProduct = productService.updateProduct(productId, product);
            
            Map<String, Object> response = new HashMap<>();
            response.put("message", "Product updated successfully");
            response.put("product", updatedProduct);
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            Map<String, String> errorResponse = new HashMap<>();
            errorResponse.put("error", e.getMessage());
            return ResponseEntity.badRequest().body(errorResponse);
        }
    }

    /**
     * US009: Delete Product (Admin only)
     */
    @DeleteMapping("/delete/{productId}")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<?> deleteProduct(@PathVariable Long productId) {
        try {
            productService.deleteProduct(productId);
            
            Map<String, String> response = new HashMap<>();
            response.put("message", "Product deleted successfully");
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            Map<String, String> errorResponse = new HashMap<>();
            errorResponse.put("error", e.getMessage());
            return ResponseEntity.badRequest().body(errorResponse);
        }
    }

    /**
     * Get product by ID
     */
    @GetMapping("/{productId}")
    @PreAuthorize("hasRole('CUSTOMER') or hasRole('ADMIN')")
    public ResponseEntity<?> getProductById(@PathVariable Long productId) {
        try {
            Optional<Product> productOpt = productService.findById(productId);
            if (productOpt.isEmpty()) {
                Map<String, String> errorResponse = new HashMap<>();
                errorResponse.put("error", "Product not found");
                return ResponseEntity.notFound().build();
            }
            
            Product product = productOpt.get();
            
            Map<String, Object> response = new HashMap<>();
            response.put("message", "Product details retrieved successfully");
            response.put("product", product);
            response.put("availableQuantity", product.getAvailableQuantity());
            response.put("isAvailable", product.isAvailable());
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            Map<String, String> errorResponse = new HashMap<>();
            errorResponse.put("error", e.getMessage());
            return ResponseEntity.badRequest().body(errorResponse);
        }
    }

    /**
     * Get all products
     */
    @GetMapping
    @PreAuthorize("hasRole('CUSTOMER') or hasRole('ADMIN')")
    public ResponseEntity<?> getAllProducts() {
        try {
            List<Product> products = productService.findAllProducts();
            
            Map<String, Object> response = new HashMap<>();
            response.put("message", "All products retrieved successfully");
            response.put("products", products);
            response.put("totalProducts", products.size());
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            Map<String, String> errorResponse = new HashMap<>();
            errorResponse.put("error", e.getMessage());
            return ResponseEntity.badRequest().body(errorResponse);
        }
    }

    /**
     * Get available products only
     */
    @GetMapping("/available")
    @PreAuthorize("hasRole('CUSTOMER') or hasRole('ADMIN')")
    public ResponseEntity<?> getAvailableProducts() {
        try {
            List<Product> products = productService.findAvailableProducts();
            
            Map<String, Object> response = new HashMap<>();
            response.put("message", "Available products retrieved successfully");
            response.put("products", products);
            response.put("totalAvailable", products.size());
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            Map<String, String> errorResponse = new HashMap<>();
            errorResponse.put("error", e.getMessage());
            return ResponseEntity.badRequest().body(errorResponse);
        }
    }

    /**
     * Update product quantity (Admin only)
     */
    @PutMapping("/{productId}/quantity")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<?> updateProductQuantity(@PathVariable Long productId, 
                                                 @RequestParam int quantity) {
        try {
            boolean updated = productService.updateQuantity(productId, quantity);
            
            if (updated) {
                Map<String, String> response = new HashMap<>();
                response.put("message", "Product quantity updated successfully");
                return ResponseEntity.ok(response);
            } else {
                Map<String, String> errorResponse = new HashMap<>();
                errorResponse.put("error", "Failed to update product quantity");
                return ResponseEntity.badRequest().body(errorResponse);
            }
            
        } catch (Exception e) {
            Map<String, String> errorResponse = new HashMap<>();
            errorResponse.put("error", e.getMessage());
            return ResponseEntity.badRequest().body(errorResponse);
        }
    }
}
