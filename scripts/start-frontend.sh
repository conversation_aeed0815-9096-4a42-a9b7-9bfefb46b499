#!/bin/bash

# Online Grocery System - Frontend Startup Script
# This script starts the Angular frontend application

echo "=================================================="
echo "Starting Online Grocery System Frontend"
echo "=================================================="

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo "Error: Node.js is not installed or not in PATH"
    echo "Please install Node.js 18 or higher"
    exit 1
fi

# Check Node.js version
NODE_VERSION=$(node -v | cut -d'v' -f2 | cut -d'.' -f1)
if [ "$NODE_VERSION" -lt 18 ]; then
    echo "Error: Node.js 18 or higher is required"
    echo "Current Node.js version: $(node -v)"
    exit 1
fi

# Check if npm is installed
if ! command -v npm &> /dev/null; then
    echo "Error: npm is not installed or not in PATH"
    exit 1
fi

# Navigate to frontend directory
cd frontend

# Install dependencies if node_modules doesn't exist
if [ ! -d "node_modules" ]; then
    echo "Installing dependencies..."
    npm install
    
    if [ $? -ne 0 ]; then
        echo "Error: Failed to install dependencies"
        exit 1
    fi
fi

# Check if Angular CLI is installed globally
if ! command -v ng &> /dev/null; then
    echo "Installing Angular CLI globally..."
    npm install -g @angular/cli@17
    
    if [ $? -ne 0 ]; then
        echo "Error: Failed to install Angular CLI"
        exit 1
    fi
fi

echo "Starting Angular development server..."
echo "Frontend will be available at: http://localhost:4200"
echo "Make sure the backend is running at: http://localhost:8080/api"
echo ""
echo "Press Ctrl+C to stop the server"
echo "=================================================="

# Start the Angular development server
ng serve --host 0.0.0.0 --port 4200
