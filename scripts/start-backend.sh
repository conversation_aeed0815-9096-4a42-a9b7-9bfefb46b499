#!/bin/bash

# Online Grocery System - Backend Startup Script
# This script starts the Spring Boot backend application

echo "=================================================="
echo "Starting Online Grocery System Backend"
echo "=================================================="

# Check if Java is installed
if ! command -v java &> /dev/null; then
    echo "Error: Java is not installed or not in PATH"
    echo "Please install Java 17 or higher"
    exit 1
fi

# Check Java version
JAVA_VERSION=$(java -version 2>&1 | awk -F '"' '/version/ {print $2}' | cut -d'.' -f1)
if [ "$JAVA_VERSION" -lt 17 ]; then
    echo "Error: Java 17 or higher is required"
    echo "Current Java version: $JAVA_VERSION"
    exit 1
fi

# Navigate to backend directory
cd backend

# Check if <PERSON><PERSON> is installed
if ! command -v mvn &> /dev/null; then
    echo "Error: <PERSON><PERSON> is not installed or not in PATH"
    echo "Please install Apache Maven"
    exit 1
fi

echo "Building the application..."
mvn clean compile

if [ $? -ne 0 ]; then
    echo "Error: Failed to build the application"
    exit 1
fi

echo "Starting Spring Boot application..."
echo "Backend will be available at: http://localhost:8080/api"
echo "H2 Database Console: http://localhost:8080/api/h2-console"
echo "Default Admin Login: admin / admin123"
echo ""
echo "Press Ctrl+C to stop the server"
echo "=================================================="

# Start the Spring Boot application
mvn spring-boot:run
